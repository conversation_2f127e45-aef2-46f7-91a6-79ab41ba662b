
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/Documents/ffmpeg/RecordTool/src/RecordManager.cpp" "CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o" "gcc" "CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o.d"
  "/home/<USER>/Documents/ffmpeg/RecordTool/src/ScreenRecorder.cpp" "CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o" "gcc" "CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o.d"
  "/home/<USER>/Documents/ffmpeg/RecordTool/src/StreamPublisher.cpp" "CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o" "gcc" "CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o.d"
  "/home/<USER>/Documents/ffmpeg/RecordTool/src/main.cpp" "CMakeFiles/RecordTool.dir/src/main.cpp.o" "gcc" "CMakeFiles/RecordTool.dir/src/main.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
