# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o: /home/<USER>/Documents/ffmpeg/RecordTool/src/RecordManager.cpp \
  /home/<USER>/Documents/ffmpeg/RecordTool/include/RecordManager.h \
  /home/<USER>/Documents/ffmpeg/RecordTool/include/ScreenRecorder.h \
  /home/<USER>/Documents/ffmpeg/RecordTool/include/StreamPublisher.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/c++/13/array \
  /usr/include/c++/13/atomic \
  /usr/include/c++/13/backward/auto_ptr.h \
  /usr/include/c++/13/backward/binders.h \
  /usr/include/c++/13/bit \
  /usr/include/c++/13/bits/algorithmfwd.h \
  /usr/include/c++/13/bits/align.h \
  /usr/include/c++/13/bits/alloc_traits.h \
  /usr/include/c++/13/bits/allocated_ptr.h \
  /usr/include/c++/13/bits/allocator.h \
  /usr/include/c++/13/bits/atomic_base.h \
  /usr/include/c++/13/bits/atomic_lockfree_defines.h \
  /usr/include/c++/13/bits/basic_ios.h \
  /usr/include/c++/13/bits/basic_ios.tcc \
  /usr/include/c++/13/bits/basic_string.h \
  /usr/include/c++/13/bits/basic_string.tcc \
  /usr/include/c++/13/bits/char_traits.h \
  /usr/include/c++/13/bits/charconv.h \
  /usr/include/c++/13/bits/chrono.h \
  /usr/include/c++/13/bits/codecvt.h \
  /usr/include/c++/13/bits/concept_check.h \
  /usr/include/c++/13/bits/cpp_type_traits.h \
  /usr/include/c++/13/bits/cxxabi_forced.h \
  /usr/include/c++/13/bits/cxxabi_init_exception.h \
  /usr/include/c++/13/bits/deque.tcc \
  /usr/include/c++/13/bits/enable_special_members.h \
  /usr/include/c++/13/bits/erase_if.h \
  /usr/include/c++/13/bits/exception.h \
  /usr/include/c++/13/bits/exception_defines.h \
  /usr/include/c++/13/bits/exception_ptr.h \
  /usr/include/c++/13/bits/fs_dir.h \
  /usr/include/c++/13/bits/fs_fwd.h \
  /usr/include/c++/13/bits/fs_ops.h \
  /usr/include/c++/13/bits/fs_path.h \
  /usr/include/c++/13/bits/functexcept.h \
  /usr/include/c++/13/bits/functional_hash.h \
  /usr/include/c++/13/bits/hash_bytes.h \
  /usr/include/c++/13/bits/hashtable.h \
  /usr/include/c++/13/bits/hashtable_policy.h \
  /usr/include/c++/13/bits/invoke.h \
  /usr/include/c++/13/bits/ios_base.h \
  /usr/include/c++/13/bits/istream.tcc \
  /usr/include/c++/13/bits/locale_classes.h \
  /usr/include/c++/13/bits/locale_classes.tcc \
  /usr/include/c++/13/bits/locale_conv.h \
  /usr/include/c++/13/bits/locale_facets.h \
  /usr/include/c++/13/bits/locale_facets.tcc \
  /usr/include/c++/13/bits/locale_facets_nonio.h \
  /usr/include/c++/13/bits/locale_facets_nonio.tcc \
  /usr/include/c++/13/bits/localefwd.h \
  /usr/include/c++/13/bits/memory_resource.h \
  /usr/include/c++/13/bits/memoryfwd.h \
  /usr/include/c++/13/bits/move.h \
  /usr/include/c++/13/bits/nested_exception.h \
  /usr/include/c++/13/bits/new_allocator.h \
  /usr/include/c++/13/bits/node_handle.h \
  /usr/include/c++/13/bits/ostream.tcc \
  /usr/include/c++/13/bits/ostream_insert.h \
  /usr/include/c++/13/bits/parse_numbers.h \
  /usr/include/c++/13/bits/postypes.h \
  /usr/include/c++/13/bits/predefined_ops.h \
  /usr/include/c++/13/bits/ptr_traits.h \
  /usr/include/c++/13/bits/quoted_string.h \
  /usr/include/c++/13/bits/range_access.h \
  /usr/include/c++/13/bits/refwrap.h \
  /usr/include/c++/13/bits/requires_hosted.h \
  /usr/include/c++/13/bits/shared_ptr.h \
  /usr/include/c++/13/bits/shared_ptr_atomic.h \
  /usr/include/c++/13/bits/shared_ptr_base.h \
  /usr/include/c++/13/bits/specfun.h \
  /usr/include/c++/13/bits/sstream.tcc \
  /usr/include/c++/13/bits/std_abs.h \
  /usr/include/c++/13/bits/std_function.h \
  /usr/include/c++/13/bits/std_mutex.h \
  /usr/include/c++/13/bits/std_thread.h \
  /usr/include/c++/13/bits/stl_algo.h \
  /usr/include/c++/13/bits/stl_algobase.h \
  /usr/include/c++/13/bits/stl_bvector.h \
  /usr/include/c++/13/bits/stl_construct.h \
  /usr/include/c++/13/bits/stl_deque.h \
  /usr/include/c++/13/bits/stl_function.h \
  /usr/include/c++/13/bits/stl_heap.h \
  /usr/include/c++/13/bits/stl_iterator.h \
  /usr/include/c++/13/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/13/bits/stl_iterator_base_types.h \
  /usr/include/c++/13/bits/stl_pair.h \
  /usr/include/c++/13/bits/stl_queue.h \
  /usr/include/c++/13/bits/stl_raw_storage_iter.h \
  /usr/include/c++/13/bits/stl_tempbuf.h \
  /usr/include/c++/13/bits/stl_uninitialized.h \
  /usr/include/c++/13/bits/stl_vector.h \
  /usr/include/c++/13/bits/streambuf.tcc \
  /usr/include/c++/13/bits/streambuf_iterator.h \
  /usr/include/c++/13/bits/string_view.tcc \
  /usr/include/c++/13/bits/stringfwd.h \
  /usr/include/c++/13/bits/this_thread_sleep.h \
  /usr/include/c++/13/bits/uniform_int_dist.h \
  /usr/include/c++/13/bits/unique_lock.h \
  /usr/include/c++/13/bits/unique_ptr.h \
  /usr/include/c++/13/bits/unordered_map.h \
  /usr/include/c++/13/bits/uses_allocator.h \
  /usr/include/c++/13/bits/uses_allocator_args.h \
  /usr/include/c++/13/bits/utility.h \
  /usr/include/c++/13/bits/vector.tcc \
  /usr/include/c++/13/cctype \
  /usr/include/c++/13/cerrno \
  /usr/include/c++/13/chrono \
  /usr/include/c++/13/clocale \
  /usr/include/c++/13/cmath \
  /usr/include/c++/13/codecvt \
  /usr/include/c++/13/compare \
  /usr/include/c++/13/condition_variable \
  /usr/include/c++/13/cstddef \
  /usr/include/c++/13/cstdint \
  /usr/include/c++/13/cstdio \
  /usr/include/c++/13/cstdlib \
  /usr/include/c++/13/ctime \
  /usr/include/c++/13/cwchar \
  /usr/include/c++/13/cwctype \
  /usr/include/c++/13/debug/assertions.h \
  /usr/include/c++/13/debug/debug.h \
  /usr/include/c++/13/deque \
  /usr/include/c++/13/exception \
  /usr/include/c++/13/ext/aligned_buffer.h \
  /usr/include/c++/13/ext/alloc_traits.h \
  /usr/include/c++/13/ext/atomicity.h \
  /usr/include/c++/13/ext/concurrence.h \
  /usr/include/c++/13/ext/numeric_traits.h \
  /usr/include/c++/13/ext/string_conversions.h \
  /usr/include/c++/13/ext/type_traits.h \
  /usr/include/c++/13/filesystem \
  /usr/include/c++/13/functional \
  /usr/include/c++/13/initializer_list \
  /usr/include/c++/13/iomanip \
  /usr/include/c++/13/ios \
  /usr/include/c++/13/iosfwd \
  /usr/include/c++/13/iostream \
  /usr/include/c++/13/istream \
  /usr/include/c++/13/limits \
  /usr/include/c++/13/locale \
  /usr/include/c++/13/math.h \
  /usr/include/c++/13/memory \
  /usr/include/c++/13/mutex \
  /usr/include/c++/13/new \
  /usr/include/c++/13/ostream \
  /usr/include/c++/13/pstl/execution_defs.h \
  /usr/include/c++/13/pstl/glue_memory_defs.h \
  /usr/include/c++/13/pstl/pstl_config.h \
  /usr/include/c++/13/queue \
  /usr/include/c++/13/ratio \
  /usr/include/c++/13/sstream \
  /usr/include/c++/13/stdexcept \
  /usr/include/c++/13/stdlib.h \
  /usr/include/c++/13/streambuf \
  /usr/include/c++/13/string \
  /usr/include/c++/13/string_view \
  /usr/include/c++/13/system_error \
  /usr/include/c++/13/thread \
  /usr/include/c++/13/tr1/bessel_function.tcc \
  /usr/include/c++/13/tr1/beta_function.tcc \
  /usr/include/c++/13/tr1/ell_integral.tcc \
  /usr/include/c++/13/tr1/exp_integral.tcc \
  /usr/include/c++/13/tr1/gamma.tcc \
  /usr/include/c++/13/tr1/hypergeometric.tcc \
  /usr/include/c++/13/tr1/legendre_function.tcc \
  /usr/include/c++/13/tr1/modified_bessel_func.tcc \
  /usr/include/c++/13/tr1/poly_hermite.tcc \
  /usr/include/c++/13/tr1/poly_laguerre.tcc \
  /usr/include/c++/13/tr1/riemann_zeta.tcc \
  /usr/include/c++/13/tr1/special_function_util.h \
  /usr/include/c++/13/tuple \
  /usr/include/c++/13/type_traits \
  /usr/include/c++/13/typeinfo \
  /usr/include/c++/13/unordered_map \
  /usr/include/c++/13/vector \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/inttypes.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/time.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/include/x86_64-linux-gnu/asm/errno.h \
  /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
  /usr/include/x86_64-linux-gnu/bits/byteswap.h \
  /usr/include/x86_64-linux-gnu/bits/cpu-set.h \
  /usr/include/x86_64-linux-gnu/bits/endian.h \
  /usr/include/x86_64-linux-gnu/bits/endianness.h \
  /usr/include/x86_64-linux-gnu/bits/errno.h \
  /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
  /usr/include/x86_64-linux-gnu/bits/floatn.h \
  /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
  /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
  /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
  /usr/include/x86_64-linux-gnu/bits/iscanonical.h \
  /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
  /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
  /usr/include/x86_64-linux-gnu/bits/local_lim.h \
  /usr/include/x86_64-linux-gnu/bits/locale.h \
  /usr/include/x86_64-linux-gnu/bits/long-double.h \
  /usr/include/x86_64-linux-gnu/bits/math-vector.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
  /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
  /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
  /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
  /usr/include/x86_64-linux-gnu/bits/sched.h \
  /usr/include/x86_64-linux-gnu/bits/select-decl.h \
  /usr/include/x86_64-linux-gnu/bits/select.h \
  /usr/include/x86_64-linux-gnu/bits/select2.h \
  /usr/include/x86_64-linux-gnu/bits/setjmp.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-least.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
  /usr/include/x86_64-linux-gnu/bits/stdio.h \
  /usr/include/x86_64-linux-gnu/bits/stdio2-decl.h \
  /usr/include/x86_64-linux-gnu/bits/stdio2.h \
  /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib.h \
  /usr/include/x86_64-linux-gnu/bits/string_fortified.h \
  /usr/include/x86_64-linux-gnu/bits/strings_fortified.h \
  /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
  /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h \
  /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
  /usr/include/x86_64-linux-gnu/bits/time.h \
  /usr/include/x86_64-linux-gnu/bits/time64.h \
  /usr/include/x86_64-linux-gnu/bits/timesize.h \
  /usr/include/x86_64-linux-gnu/bits/timex.h \
  /usr/include/x86_64-linux-gnu/bits/types.h \
  /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/error_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
  /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
  /usr/include/x86_64-linux-gnu/bits/typesizes.h \
  /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
  /usr/include/x86_64-linux-gnu/bits/uio_lim.h \
  /usr/include/x86_64-linux-gnu/bits/waitflags.h \
  /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
  /usr/include/x86_64-linux-gnu/bits/wchar.h \
  /usr/include/x86_64-linux-gnu/bits/wchar2-decl.h \
  /usr/include/x86_64-linux-gnu/bits/wchar2.h \
  /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h \
  /usr/include/x86_64-linux-gnu/bits/wordsize.h \
  /usr/include/x86_64-linux-gnu/bits/xopen_lim.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/atomic_word.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/c++allocator.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/c++config.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/c++locale.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/cpu_defines.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/ctype_base.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/ctype_inline.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/error_constants.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/gthr-default.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/gthr.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/messages_members.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/os_defines.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/time_members.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs.h \
  /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h \
  /usr/include/x86_64-linux-gnu/libavcodec/defs.h \
  /usr/include/x86_64-linux-gnu/libavcodec/packet.h \
  /usr/include/x86_64-linux-gnu/libavcodec/version.h \
  /usr/include/x86_64-linux-gnu/libavcodec/version_major.h \
  /usr/include/x86_64-linux-gnu/libavdevice/avdevice.h \
  /usr/include/x86_64-linux-gnu/libavdevice/version.h \
  /usr/include/x86_64-linux-gnu/libavdevice/version_major.h \
  /usr/include/x86_64-linux-gnu/libavformat/avformat.h \
  /usr/include/x86_64-linux-gnu/libavformat/avio.h \
  /usr/include/x86_64-linux-gnu/libavformat/version.h \
  /usr/include/x86_64-linux-gnu/libavformat/version_major.h \
  /usr/include/x86_64-linux-gnu/libavutil/attributes.h \
  /usr/include/x86_64-linux-gnu/libavutil/avconfig.h \
  /usr/include/x86_64-linux-gnu/libavutil/avutil.h \
  /usr/include/x86_64-linux-gnu/libavutil/buffer.h \
  /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h \
  /usr/include/x86_64-linux-gnu/libavutil/common.h \
  /usr/include/x86_64-linux-gnu/libavutil/dict.h \
  /usr/include/x86_64-linux-gnu/libavutil/error.h \
  /usr/include/x86_64-linux-gnu/libavutil/frame.h \
  /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h \
  /usr/include/x86_64-linux-gnu/libavutil/imgutils.h \
  /usr/include/x86_64-linux-gnu/libavutil/intfloat.h \
  /usr/include/x86_64-linux-gnu/libavutil/log.h \
  /usr/include/x86_64-linux-gnu/libavutil/macros.h \
  /usr/include/x86_64-linux-gnu/libavutil/mathematics.h \
  /usr/include/x86_64-linux-gnu/libavutil/mem.h \
  /usr/include/x86_64-linux-gnu/libavutil/opt.h \
  /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h \
  /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h \
  /usr/include/x86_64-linux-gnu/libavutil/rational.h \
  /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h \
  /usr/include/x86_64-linux-gnu/libavutil/time.h \
  /usr/include/x86_64-linux-gnu/libavutil/version.h \
  /usr/include/x86_64-linux-gnu/libswscale/swscale.h \
  /usr/include/x86_64-linux-gnu/libswscale/version.h \
  /usr/include/x86_64-linux-gnu/libswscale/version_major.h \
  /usr/include/x86_64-linux-gnu/sys/cdefs.h \
  /usr/include/x86_64-linux-gnu/sys/select.h \
  /usr/include/x86_64-linux-gnu/sys/single_threaded.h \
  /usr/include/x86_64-linux-gnu/sys/types.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/limits.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/stdarg.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/stddef.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/stdint.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/syslimits.h

CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o: /home/<USER>/Documents/ffmpeg/RecordTool/src/ScreenRecorder.cpp \
  /home/<USER>/Documents/ffmpeg/RecordTool/include/ScreenRecorder.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/c++/13/atomic \
  /usr/include/c++/13/backward/auto_ptr.h \
  /usr/include/c++/13/backward/binders.h \
  /usr/include/c++/13/bit \
  /usr/include/c++/13/bits/align.h \
  /usr/include/c++/13/bits/alloc_traits.h \
  /usr/include/c++/13/bits/allocated_ptr.h \
  /usr/include/c++/13/bits/allocator.h \
  /usr/include/c++/13/bits/atomic_base.h \
  /usr/include/c++/13/bits/atomic_lockfree_defines.h \
  /usr/include/c++/13/bits/basic_ios.h \
  /usr/include/c++/13/bits/basic_ios.tcc \
  /usr/include/c++/13/bits/basic_string.h \
  /usr/include/c++/13/bits/basic_string.tcc \
  /usr/include/c++/13/bits/char_traits.h \
  /usr/include/c++/13/bits/charconv.h \
  /usr/include/c++/13/bits/chrono.h \
  /usr/include/c++/13/bits/concept_check.h \
  /usr/include/c++/13/bits/cpp_type_traits.h \
  /usr/include/c++/13/bits/cxxabi_forced.h \
  /usr/include/c++/13/bits/cxxabi_init_exception.h \
  /usr/include/c++/13/bits/exception.h \
  /usr/include/c++/13/bits/exception_defines.h \
  /usr/include/c++/13/bits/exception_ptr.h \
  /usr/include/c++/13/bits/functexcept.h \
  /usr/include/c++/13/bits/functional_hash.h \
  /usr/include/c++/13/bits/hash_bytes.h \
  /usr/include/c++/13/bits/invoke.h \
  /usr/include/c++/13/bits/ios_base.h \
  /usr/include/c++/13/bits/istream.tcc \
  /usr/include/c++/13/bits/locale_classes.h \
  /usr/include/c++/13/bits/locale_classes.tcc \
  /usr/include/c++/13/bits/locale_facets.h \
  /usr/include/c++/13/bits/locale_facets.tcc \
  /usr/include/c++/13/bits/localefwd.h \
  /usr/include/c++/13/bits/memory_resource.h \
  /usr/include/c++/13/bits/memoryfwd.h \
  /usr/include/c++/13/bits/move.h \
  /usr/include/c++/13/bits/nested_exception.h \
  /usr/include/c++/13/bits/new_allocator.h \
  /usr/include/c++/13/bits/ostream.tcc \
  /usr/include/c++/13/bits/ostream_insert.h \
  /usr/include/c++/13/bits/parse_numbers.h \
  /usr/include/c++/13/bits/postypes.h \
  /usr/include/c++/13/bits/predefined_ops.h \
  /usr/include/c++/13/bits/ptr_traits.h \
  /usr/include/c++/13/bits/range_access.h \
  /usr/include/c++/13/bits/refwrap.h \
  /usr/include/c++/13/bits/requires_hosted.h \
  /usr/include/c++/13/bits/shared_ptr.h \
  /usr/include/c++/13/bits/shared_ptr_atomic.h \
  /usr/include/c++/13/bits/shared_ptr_base.h \
  /usr/include/c++/13/bits/specfun.h \
  /usr/include/c++/13/bits/std_abs.h \
  /usr/include/c++/13/bits/std_mutex.h \
  /usr/include/c++/13/bits/std_thread.h \
  /usr/include/c++/13/bits/stl_algobase.h \
  /usr/include/c++/13/bits/stl_construct.h \
  /usr/include/c++/13/bits/stl_function.h \
  /usr/include/c++/13/bits/stl_iterator.h \
  /usr/include/c++/13/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/13/bits/stl_iterator_base_types.h \
  /usr/include/c++/13/bits/stl_pair.h \
  /usr/include/c++/13/bits/stl_raw_storage_iter.h \
  /usr/include/c++/13/bits/stl_tempbuf.h \
  /usr/include/c++/13/bits/stl_uninitialized.h \
  /usr/include/c++/13/bits/streambuf.tcc \
  /usr/include/c++/13/bits/streambuf_iterator.h \
  /usr/include/c++/13/bits/string_view.tcc \
  /usr/include/c++/13/bits/stringfwd.h \
  /usr/include/c++/13/bits/this_thread_sleep.h \
  /usr/include/c++/13/bits/unique_lock.h \
  /usr/include/c++/13/bits/unique_ptr.h \
  /usr/include/c++/13/bits/uses_allocator.h \
  /usr/include/c++/13/bits/uses_allocator_args.h \
  /usr/include/c++/13/bits/utility.h \
  /usr/include/c++/13/cctype \
  /usr/include/c++/13/cerrno \
  /usr/include/c++/13/clocale \
  /usr/include/c++/13/cmath \
  /usr/include/c++/13/cstddef \
  /usr/include/c++/13/cstdint \
  /usr/include/c++/13/cstdio \
  /usr/include/c++/13/cstdlib \
  /usr/include/c++/13/cstring \
  /usr/include/c++/13/ctime \
  /usr/include/c++/13/cwchar \
  /usr/include/c++/13/cwctype \
  /usr/include/c++/13/debug/assertions.h \
  /usr/include/c++/13/debug/debug.h \
  /usr/include/c++/13/exception \
  /usr/include/c++/13/ext/aligned_buffer.h \
  /usr/include/c++/13/ext/alloc_traits.h \
  /usr/include/c++/13/ext/atomicity.h \
  /usr/include/c++/13/ext/concurrence.h \
  /usr/include/c++/13/ext/numeric_traits.h \
  /usr/include/c++/13/ext/string_conversions.h \
  /usr/include/c++/13/ext/type_traits.h \
  /usr/include/c++/13/initializer_list \
  /usr/include/c++/13/ios \
  /usr/include/c++/13/iosfwd \
  /usr/include/c++/13/iostream \
  /usr/include/c++/13/istream \
  /usr/include/c++/13/limits \
  /usr/include/c++/13/math.h \
  /usr/include/c++/13/memory \
  /usr/include/c++/13/mutex \
  /usr/include/c++/13/new \
  /usr/include/c++/13/ostream \
  /usr/include/c++/13/pstl/execution_defs.h \
  /usr/include/c++/13/pstl/glue_memory_defs.h \
  /usr/include/c++/13/pstl/pstl_config.h \
  /usr/include/c++/13/ratio \
  /usr/include/c++/13/stdexcept \
  /usr/include/c++/13/stdlib.h \
  /usr/include/c++/13/streambuf \
  /usr/include/c++/13/string \
  /usr/include/c++/13/string_view \
  /usr/include/c++/13/system_error \
  /usr/include/c++/13/thread \
  /usr/include/c++/13/tr1/bessel_function.tcc \
  /usr/include/c++/13/tr1/beta_function.tcc \
  /usr/include/c++/13/tr1/ell_integral.tcc \
  /usr/include/c++/13/tr1/exp_integral.tcc \
  /usr/include/c++/13/tr1/gamma.tcc \
  /usr/include/c++/13/tr1/hypergeometric.tcc \
  /usr/include/c++/13/tr1/legendre_function.tcc \
  /usr/include/c++/13/tr1/modified_bessel_func.tcc \
  /usr/include/c++/13/tr1/poly_hermite.tcc \
  /usr/include/c++/13/tr1/poly_laguerre.tcc \
  /usr/include/c++/13/tr1/riemann_zeta.tcc \
  /usr/include/c++/13/tr1/special_function_util.h \
  /usr/include/c++/13/tuple \
  /usr/include/c++/13/type_traits \
  /usr/include/c++/13/typeinfo \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/inttypes.h \
  /usr/include/limits.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/time.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/include/x86_64-linux-gnu/asm/errno.h \
  /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
  /usr/include/x86_64-linux-gnu/bits/byteswap.h \
  /usr/include/x86_64-linux-gnu/bits/cpu-set.h \
  /usr/include/x86_64-linux-gnu/bits/endian.h \
  /usr/include/x86_64-linux-gnu/bits/endianness.h \
  /usr/include/x86_64-linux-gnu/bits/errno.h \
  /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
  /usr/include/x86_64-linux-gnu/bits/floatn.h \
  /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
  /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
  /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
  /usr/include/x86_64-linux-gnu/bits/iscanonical.h \
  /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
  /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
  /usr/include/x86_64-linux-gnu/bits/local_lim.h \
  /usr/include/x86_64-linux-gnu/bits/locale.h \
  /usr/include/x86_64-linux-gnu/bits/long-double.h \
  /usr/include/x86_64-linux-gnu/bits/math-vector.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
  /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
  /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
  /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
  /usr/include/x86_64-linux-gnu/bits/sched.h \
  /usr/include/x86_64-linux-gnu/bits/select-decl.h \
  /usr/include/x86_64-linux-gnu/bits/select.h \
  /usr/include/x86_64-linux-gnu/bits/select2.h \
  /usr/include/x86_64-linux-gnu/bits/setjmp.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-least.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
  /usr/include/x86_64-linux-gnu/bits/stdio.h \
  /usr/include/x86_64-linux-gnu/bits/stdio2-decl.h \
  /usr/include/x86_64-linux-gnu/bits/stdio2.h \
  /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib.h \
  /usr/include/x86_64-linux-gnu/bits/string_fortified.h \
  /usr/include/x86_64-linux-gnu/bits/strings_fortified.h \
  /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
  /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h \
  /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
  /usr/include/x86_64-linux-gnu/bits/time.h \
  /usr/include/x86_64-linux-gnu/bits/time64.h \
  /usr/include/x86_64-linux-gnu/bits/timesize.h \
  /usr/include/x86_64-linux-gnu/bits/timex.h \
  /usr/include/x86_64-linux-gnu/bits/types.h \
  /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/error_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
  /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
  /usr/include/x86_64-linux-gnu/bits/typesizes.h \
  /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
  /usr/include/x86_64-linux-gnu/bits/uio_lim.h \
  /usr/include/x86_64-linux-gnu/bits/waitflags.h \
  /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
  /usr/include/x86_64-linux-gnu/bits/wchar.h \
  /usr/include/x86_64-linux-gnu/bits/wchar2-decl.h \
  /usr/include/x86_64-linux-gnu/bits/wchar2.h \
  /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h \
  /usr/include/x86_64-linux-gnu/bits/wordsize.h \
  /usr/include/x86_64-linux-gnu/bits/xopen_lim.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/atomic_word.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/c++allocator.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/c++config.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/c++locale.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/cpu_defines.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/ctype_base.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/ctype_inline.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/error_constants.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/gthr-default.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/gthr.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/os_defines.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs.h \
  /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h \
  /usr/include/x86_64-linux-gnu/libavcodec/defs.h \
  /usr/include/x86_64-linux-gnu/libavcodec/packet.h \
  /usr/include/x86_64-linux-gnu/libavcodec/version.h \
  /usr/include/x86_64-linux-gnu/libavcodec/version_major.h \
  /usr/include/x86_64-linux-gnu/libavdevice/avdevice.h \
  /usr/include/x86_64-linux-gnu/libavdevice/version.h \
  /usr/include/x86_64-linux-gnu/libavdevice/version_major.h \
  /usr/include/x86_64-linux-gnu/libavformat/avformat.h \
  /usr/include/x86_64-linux-gnu/libavformat/avio.h \
  /usr/include/x86_64-linux-gnu/libavformat/version.h \
  /usr/include/x86_64-linux-gnu/libavformat/version_major.h \
  /usr/include/x86_64-linux-gnu/libavutil/attributes.h \
  /usr/include/x86_64-linux-gnu/libavutil/avconfig.h \
  /usr/include/x86_64-linux-gnu/libavutil/avutil.h \
  /usr/include/x86_64-linux-gnu/libavutil/buffer.h \
  /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h \
  /usr/include/x86_64-linux-gnu/libavutil/common.h \
  /usr/include/x86_64-linux-gnu/libavutil/dict.h \
  /usr/include/x86_64-linux-gnu/libavutil/error.h \
  /usr/include/x86_64-linux-gnu/libavutil/frame.h \
  /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h \
  /usr/include/x86_64-linux-gnu/libavutil/imgutils.h \
  /usr/include/x86_64-linux-gnu/libavutil/intfloat.h \
  /usr/include/x86_64-linux-gnu/libavutil/log.h \
  /usr/include/x86_64-linux-gnu/libavutil/macros.h \
  /usr/include/x86_64-linux-gnu/libavutil/mathematics.h \
  /usr/include/x86_64-linux-gnu/libavutil/mem.h \
  /usr/include/x86_64-linux-gnu/libavutil/opt.h \
  /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h \
  /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h \
  /usr/include/x86_64-linux-gnu/libavutil/rational.h \
  /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h \
  /usr/include/x86_64-linux-gnu/libavutil/time.h \
  /usr/include/x86_64-linux-gnu/libavutil/version.h \
  /usr/include/x86_64-linux-gnu/libswscale/swscale.h \
  /usr/include/x86_64-linux-gnu/libswscale/version.h \
  /usr/include/x86_64-linux-gnu/libswscale/version_major.h \
  /usr/include/x86_64-linux-gnu/sys/cdefs.h \
  /usr/include/x86_64-linux-gnu/sys/select.h \
  /usr/include/x86_64-linux-gnu/sys/single_threaded.h \
  /usr/include/x86_64-linux-gnu/sys/types.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/limits.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/stdarg.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/stddef.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/stdint.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/syslimits.h

CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o: /home/<USER>/Documents/ffmpeg/RecordTool/src/StreamPublisher.cpp \
  /home/<USER>/Documents/ffmpeg/RecordTool/include/StreamPublisher.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/c++/13/atomic \
  /usr/include/c++/13/backward/auto_ptr.h \
  /usr/include/c++/13/backward/binders.h \
  /usr/include/c++/13/bit \
  /usr/include/c++/13/bits/align.h \
  /usr/include/c++/13/bits/alloc_traits.h \
  /usr/include/c++/13/bits/allocated_ptr.h \
  /usr/include/c++/13/bits/allocator.h \
  /usr/include/c++/13/bits/atomic_base.h \
  /usr/include/c++/13/bits/atomic_lockfree_defines.h \
  /usr/include/c++/13/bits/basic_ios.h \
  /usr/include/c++/13/bits/basic_ios.tcc \
  /usr/include/c++/13/bits/basic_string.h \
  /usr/include/c++/13/bits/basic_string.tcc \
  /usr/include/c++/13/bits/char_traits.h \
  /usr/include/c++/13/bits/charconv.h \
  /usr/include/c++/13/bits/chrono.h \
  /usr/include/c++/13/bits/concept_check.h \
  /usr/include/c++/13/bits/cpp_type_traits.h \
  /usr/include/c++/13/bits/cxxabi_forced.h \
  /usr/include/c++/13/bits/cxxabi_init_exception.h \
  /usr/include/c++/13/bits/deque.tcc \
  /usr/include/c++/13/bits/exception.h \
  /usr/include/c++/13/bits/exception_defines.h \
  /usr/include/c++/13/bits/exception_ptr.h \
  /usr/include/c++/13/bits/functexcept.h \
  /usr/include/c++/13/bits/functional_hash.h \
  /usr/include/c++/13/bits/hash_bytes.h \
  /usr/include/c++/13/bits/invoke.h \
  /usr/include/c++/13/bits/ios_base.h \
  /usr/include/c++/13/bits/istream.tcc \
  /usr/include/c++/13/bits/locale_classes.h \
  /usr/include/c++/13/bits/locale_classes.tcc \
  /usr/include/c++/13/bits/locale_facets.h \
  /usr/include/c++/13/bits/locale_facets.tcc \
  /usr/include/c++/13/bits/localefwd.h \
  /usr/include/c++/13/bits/memory_resource.h \
  /usr/include/c++/13/bits/memoryfwd.h \
  /usr/include/c++/13/bits/move.h \
  /usr/include/c++/13/bits/nested_exception.h \
  /usr/include/c++/13/bits/new_allocator.h \
  /usr/include/c++/13/bits/ostream.tcc \
  /usr/include/c++/13/bits/ostream_insert.h \
  /usr/include/c++/13/bits/parse_numbers.h \
  /usr/include/c++/13/bits/postypes.h \
  /usr/include/c++/13/bits/predefined_ops.h \
  /usr/include/c++/13/bits/ptr_traits.h \
  /usr/include/c++/13/bits/range_access.h \
  /usr/include/c++/13/bits/refwrap.h \
  /usr/include/c++/13/bits/requires_hosted.h \
  /usr/include/c++/13/bits/shared_ptr.h \
  /usr/include/c++/13/bits/shared_ptr_atomic.h \
  /usr/include/c++/13/bits/shared_ptr_base.h \
  /usr/include/c++/13/bits/specfun.h \
  /usr/include/c++/13/bits/std_abs.h \
  /usr/include/c++/13/bits/std_mutex.h \
  /usr/include/c++/13/bits/std_thread.h \
  /usr/include/c++/13/bits/stl_algobase.h \
  /usr/include/c++/13/bits/stl_bvector.h \
  /usr/include/c++/13/bits/stl_construct.h \
  /usr/include/c++/13/bits/stl_deque.h \
  /usr/include/c++/13/bits/stl_function.h \
  /usr/include/c++/13/bits/stl_heap.h \
  /usr/include/c++/13/bits/stl_iterator.h \
  /usr/include/c++/13/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/13/bits/stl_iterator_base_types.h \
  /usr/include/c++/13/bits/stl_pair.h \
  /usr/include/c++/13/bits/stl_queue.h \
  /usr/include/c++/13/bits/stl_raw_storage_iter.h \
  /usr/include/c++/13/bits/stl_tempbuf.h \
  /usr/include/c++/13/bits/stl_uninitialized.h \
  /usr/include/c++/13/bits/stl_vector.h \
  /usr/include/c++/13/bits/streambuf.tcc \
  /usr/include/c++/13/bits/streambuf_iterator.h \
  /usr/include/c++/13/bits/string_view.tcc \
  /usr/include/c++/13/bits/stringfwd.h \
  /usr/include/c++/13/bits/this_thread_sleep.h \
  /usr/include/c++/13/bits/unique_lock.h \
  /usr/include/c++/13/bits/unique_ptr.h \
  /usr/include/c++/13/bits/uses_allocator.h \
  /usr/include/c++/13/bits/uses_allocator_args.h \
  /usr/include/c++/13/bits/utility.h \
  /usr/include/c++/13/bits/vector.tcc \
  /usr/include/c++/13/cctype \
  /usr/include/c++/13/cerrno \
  /usr/include/c++/13/clocale \
  /usr/include/c++/13/cmath \
  /usr/include/c++/13/condition_variable \
  /usr/include/c++/13/cstddef \
  /usr/include/c++/13/cstdint \
  /usr/include/c++/13/cstdio \
  /usr/include/c++/13/cstdlib \
  /usr/include/c++/13/cstring \
  /usr/include/c++/13/ctime \
  /usr/include/c++/13/cwchar \
  /usr/include/c++/13/cwctype \
  /usr/include/c++/13/debug/assertions.h \
  /usr/include/c++/13/debug/debug.h \
  /usr/include/c++/13/deque \
  /usr/include/c++/13/exception \
  /usr/include/c++/13/ext/aligned_buffer.h \
  /usr/include/c++/13/ext/alloc_traits.h \
  /usr/include/c++/13/ext/atomicity.h \
  /usr/include/c++/13/ext/concurrence.h \
  /usr/include/c++/13/ext/numeric_traits.h \
  /usr/include/c++/13/ext/string_conversions.h \
  /usr/include/c++/13/ext/type_traits.h \
  /usr/include/c++/13/initializer_list \
  /usr/include/c++/13/ios \
  /usr/include/c++/13/iosfwd \
  /usr/include/c++/13/iostream \
  /usr/include/c++/13/istream \
  /usr/include/c++/13/limits \
  /usr/include/c++/13/math.h \
  /usr/include/c++/13/memory \
  /usr/include/c++/13/mutex \
  /usr/include/c++/13/new \
  /usr/include/c++/13/ostream \
  /usr/include/c++/13/pstl/execution_defs.h \
  /usr/include/c++/13/pstl/glue_memory_defs.h \
  /usr/include/c++/13/pstl/pstl_config.h \
  /usr/include/c++/13/queue \
  /usr/include/c++/13/ratio \
  /usr/include/c++/13/stdexcept \
  /usr/include/c++/13/stdlib.h \
  /usr/include/c++/13/streambuf \
  /usr/include/c++/13/string \
  /usr/include/c++/13/string_view \
  /usr/include/c++/13/system_error \
  /usr/include/c++/13/thread \
  /usr/include/c++/13/tr1/bessel_function.tcc \
  /usr/include/c++/13/tr1/beta_function.tcc \
  /usr/include/c++/13/tr1/ell_integral.tcc \
  /usr/include/c++/13/tr1/exp_integral.tcc \
  /usr/include/c++/13/tr1/gamma.tcc \
  /usr/include/c++/13/tr1/hypergeometric.tcc \
  /usr/include/c++/13/tr1/legendre_function.tcc \
  /usr/include/c++/13/tr1/modified_bessel_func.tcc \
  /usr/include/c++/13/tr1/poly_hermite.tcc \
  /usr/include/c++/13/tr1/poly_laguerre.tcc \
  /usr/include/c++/13/tr1/riemann_zeta.tcc \
  /usr/include/c++/13/tr1/special_function_util.h \
  /usr/include/c++/13/tuple \
  /usr/include/c++/13/type_traits \
  /usr/include/c++/13/typeinfo \
  /usr/include/c++/13/vector \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/inttypes.h \
  /usr/include/limits.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/time.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/include/x86_64-linux-gnu/asm/errno.h \
  /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
  /usr/include/x86_64-linux-gnu/bits/byteswap.h \
  /usr/include/x86_64-linux-gnu/bits/cpu-set.h \
  /usr/include/x86_64-linux-gnu/bits/endian.h \
  /usr/include/x86_64-linux-gnu/bits/endianness.h \
  /usr/include/x86_64-linux-gnu/bits/errno.h \
  /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
  /usr/include/x86_64-linux-gnu/bits/floatn.h \
  /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
  /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
  /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
  /usr/include/x86_64-linux-gnu/bits/iscanonical.h \
  /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
  /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
  /usr/include/x86_64-linux-gnu/bits/local_lim.h \
  /usr/include/x86_64-linux-gnu/bits/locale.h \
  /usr/include/x86_64-linux-gnu/bits/long-double.h \
  /usr/include/x86_64-linux-gnu/bits/math-vector.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
  /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
  /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
  /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
  /usr/include/x86_64-linux-gnu/bits/sched.h \
  /usr/include/x86_64-linux-gnu/bits/select-decl.h \
  /usr/include/x86_64-linux-gnu/bits/select.h \
  /usr/include/x86_64-linux-gnu/bits/select2.h \
  /usr/include/x86_64-linux-gnu/bits/setjmp.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-least.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
  /usr/include/x86_64-linux-gnu/bits/stdio.h \
  /usr/include/x86_64-linux-gnu/bits/stdio2-decl.h \
  /usr/include/x86_64-linux-gnu/bits/stdio2.h \
  /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib.h \
  /usr/include/x86_64-linux-gnu/bits/string_fortified.h \
  /usr/include/x86_64-linux-gnu/bits/strings_fortified.h \
  /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
  /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h \
  /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
  /usr/include/x86_64-linux-gnu/bits/time.h \
  /usr/include/x86_64-linux-gnu/bits/time64.h \
  /usr/include/x86_64-linux-gnu/bits/timesize.h \
  /usr/include/x86_64-linux-gnu/bits/timex.h \
  /usr/include/x86_64-linux-gnu/bits/types.h \
  /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/error_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
  /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
  /usr/include/x86_64-linux-gnu/bits/typesizes.h \
  /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
  /usr/include/x86_64-linux-gnu/bits/uio_lim.h \
  /usr/include/x86_64-linux-gnu/bits/waitflags.h \
  /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
  /usr/include/x86_64-linux-gnu/bits/wchar.h \
  /usr/include/x86_64-linux-gnu/bits/wchar2-decl.h \
  /usr/include/x86_64-linux-gnu/bits/wchar2.h \
  /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h \
  /usr/include/x86_64-linux-gnu/bits/wordsize.h \
  /usr/include/x86_64-linux-gnu/bits/xopen_lim.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/atomic_word.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/c++allocator.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/c++config.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/c++locale.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/cpu_defines.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/ctype_base.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/ctype_inline.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/error_constants.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/gthr-default.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/gthr.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/os_defines.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs.h \
  /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h \
  /usr/include/x86_64-linux-gnu/libavcodec/defs.h \
  /usr/include/x86_64-linux-gnu/libavcodec/packet.h \
  /usr/include/x86_64-linux-gnu/libavcodec/version.h \
  /usr/include/x86_64-linux-gnu/libavcodec/version_major.h \
  /usr/include/x86_64-linux-gnu/libavformat/avformat.h \
  /usr/include/x86_64-linux-gnu/libavformat/avio.h \
  /usr/include/x86_64-linux-gnu/libavformat/version.h \
  /usr/include/x86_64-linux-gnu/libavformat/version_major.h \
  /usr/include/x86_64-linux-gnu/libavutil/attributes.h \
  /usr/include/x86_64-linux-gnu/libavutil/avconfig.h \
  /usr/include/x86_64-linux-gnu/libavutil/avutil.h \
  /usr/include/x86_64-linux-gnu/libavutil/buffer.h \
  /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h \
  /usr/include/x86_64-linux-gnu/libavutil/common.h \
  /usr/include/x86_64-linux-gnu/libavutil/dict.h \
  /usr/include/x86_64-linux-gnu/libavutil/error.h \
  /usr/include/x86_64-linux-gnu/libavutil/frame.h \
  /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h \
  /usr/include/x86_64-linux-gnu/libavutil/intfloat.h \
  /usr/include/x86_64-linux-gnu/libavutil/log.h \
  /usr/include/x86_64-linux-gnu/libavutil/macros.h \
  /usr/include/x86_64-linux-gnu/libavutil/mathematics.h \
  /usr/include/x86_64-linux-gnu/libavutil/mem.h \
  /usr/include/x86_64-linux-gnu/libavutil/opt.h \
  /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h \
  /usr/include/x86_64-linux-gnu/libavutil/rational.h \
  /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h \
  /usr/include/x86_64-linux-gnu/libavutil/time.h \
  /usr/include/x86_64-linux-gnu/libavutil/version.h \
  /usr/include/x86_64-linux-gnu/sys/cdefs.h \
  /usr/include/x86_64-linux-gnu/sys/select.h \
  /usr/include/x86_64-linux-gnu/sys/single_threaded.h \
  /usr/include/x86_64-linux-gnu/sys/types.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/limits.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/stdarg.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/stddef.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/stdint.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/syslimits.h

CMakeFiles/RecordTool.dir/src/main.cpp.o: /home/<USER>/Documents/ffmpeg/RecordTool/src/main.cpp \
  /home/<USER>/Documents/ffmpeg/RecordTool/include/RecordManager.h \
  /home/<USER>/Documents/ffmpeg/RecordTool/include/ScreenRecorder.h \
  /home/<USER>/Documents/ffmpeg/RecordTool/include/StreamPublisher.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/c++/13/array \
  /usr/include/c++/13/atomic \
  /usr/include/c++/13/backward/auto_ptr.h \
  /usr/include/c++/13/backward/binders.h \
  /usr/include/c++/13/bit \
  /usr/include/c++/13/bits/algorithmfwd.h \
  /usr/include/c++/13/bits/align.h \
  /usr/include/c++/13/bits/alloc_traits.h \
  /usr/include/c++/13/bits/allocated_ptr.h \
  /usr/include/c++/13/bits/allocator.h \
  /usr/include/c++/13/bits/atomic_base.h \
  /usr/include/c++/13/bits/atomic_lockfree_defines.h \
  /usr/include/c++/13/bits/basic_ios.h \
  /usr/include/c++/13/bits/basic_ios.tcc \
  /usr/include/c++/13/bits/basic_string.h \
  /usr/include/c++/13/bits/basic_string.tcc \
  /usr/include/c++/13/bits/char_traits.h \
  /usr/include/c++/13/bits/charconv.h \
  /usr/include/c++/13/bits/chrono.h \
  /usr/include/c++/13/bits/codecvt.h \
  /usr/include/c++/13/bits/concept_check.h \
  /usr/include/c++/13/bits/cpp_type_traits.h \
  /usr/include/c++/13/bits/cxxabi_forced.h \
  /usr/include/c++/13/bits/cxxabi_init_exception.h \
  /usr/include/c++/13/bits/deque.tcc \
  /usr/include/c++/13/bits/enable_special_members.h \
  /usr/include/c++/13/bits/erase_if.h \
  /usr/include/c++/13/bits/exception.h \
  /usr/include/c++/13/bits/exception_defines.h \
  /usr/include/c++/13/bits/exception_ptr.h \
  /usr/include/c++/13/bits/functexcept.h \
  /usr/include/c++/13/bits/functional_hash.h \
  /usr/include/c++/13/bits/hash_bytes.h \
  /usr/include/c++/13/bits/hashtable.h \
  /usr/include/c++/13/bits/hashtable_policy.h \
  /usr/include/c++/13/bits/invoke.h \
  /usr/include/c++/13/bits/ios_base.h \
  /usr/include/c++/13/bits/istream.tcc \
  /usr/include/c++/13/bits/locale_classes.h \
  /usr/include/c++/13/bits/locale_classes.tcc \
  /usr/include/c++/13/bits/locale_conv.h \
  /usr/include/c++/13/bits/locale_facets.h \
  /usr/include/c++/13/bits/locale_facets.tcc \
  /usr/include/c++/13/bits/locale_facets_nonio.h \
  /usr/include/c++/13/bits/locale_facets_nonio.tcc \
  /usr/include/c++/13/bits/localefwd.h \
  /usr/include/c++/13/bits/memory_resource.h \
  /usr/include/c++/13/bits/memoryfwd.h \
  /usr/include/c++/13/bits/move.h \
  /usr/include/c++/13/bits/nested_exception.h \
  /usr/include/c++/13/bits/new_allocator.h \
  /usr/include/c++/13/bits/node_handle.h \
  /usr/include/c++/13/bits/ostream.tcc \
  /usr/include/c++/13/bits/ostream_insert.h \
  /usr/include/c++/13/bits/parse_numbers.h \
  /usr/include/c++/13/bits/postypes.h \
  /usr/include/c++/13/bits/predefined_ops.h \
  /usr/include/c++/13/bits/ptr_traits.h \
  /usr/include/c++/13/bits/quoted_string.h \
  /usr/include/c++/13/bits/range_access.h \
  /usr/include/c++/13/bits/refwrap.h \
  /usr/include/c++/13/bits/requires_hosted.h \
  /usr/include/c++/13/bits/shared_ptr.h \
  /usr/include/c++/13/bits/shared_ptr_atomic.h \
  /usr/include/c++/13/bits/shared_ptr_base.h \
  /usr/include/c++/13/bits/specfun.h \
  /usr/include/c++/13/bits/sstream.tcc \
  /usr/include/c++/13/bits/std_abs.h \
  /usr/include/c++/13/bits/std_function.h \
  /usr/include/c++/13/bits/std_mutex.h \
  /usr/include/c++/13/bits/std_thread.h \
  /usr/include/c++/13/bits/stl_algo.h \
  /usr/include/c++/13/bits/stl_algobase.h \
  /usr/include/c++/13/bits/stl_bvector.h \
  /usr/include/c++/13/bits/stl_construct.h \
  /usr/include/c++/13/bits/stl_deque.h \
  /usr/include/c++/13/bits/stl_function.h \
  /usr/include/c++/13/bits/stl_heap.h \
  /usr/include/c++/13/bits/stl_iterator.h \
  /usr/include/c++/13/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/13/bits/stl_iterator_base_types.h \
  /usr/include/c++/13/bits/stl_pair.h \
  /usr/include/c++/13/bits/stl_queue.h \
  /usr/include/c++/13/bits/stl_raw_storage_iter.h \
  /usr/include/c++/13/bits/stl_tempbuf.h \
  /usr/include/c++/13/bits/stl_uninitialized.h \
  /usr/include/c++/13/bits/stl_vector.h \
  /usr/include/c++/13/bits/streambuf.tcc \
  /usr/include/c++/13/bits/streambuf_iterator.h \
  /usr/include/c++/13/bits/string_view.tcc \
  /usr/include/c++/13/bits/stringfwd.h \
  /usr/include/c++/13/bits/this_thread_sleep.h \
  /usr/include/c++/13/bits/uniform_int_dist.h \
  /usr/include/c++/13/bits/unique_lock.h \
  /usr/include/c++/13/bits/unique_ptr.h \
  /usr/include/c++/13/bits/unordered_map.h \
  /usr/include/c++/13/bits/uses_allocator.h \
  /usr/include/c++/13/bits/uses_allocator_args.h \
  /usr/include/c++/13/bits/utility.h \
  /usr/include/c++/13/bits/vector.tcc \
  /usr/include/c++/13/cctype \
  /usr/include/c++/13/cerrno \
  /usr/include/c++/13/chrono \
  /usr/include/c++/13/clocale \
  /usr/include/c++/13/cmath \
  /usr/include/c++/13/compare \
  /usr/include/c++/13/condition_variable \
  /usr/include/c++/13/cstddef \
  /usr/include/c++/13/cstdint \
  /usr/include/c++/13/cstdio \
  /usr/include/c++/13/cstdlib \
  /usr/include/c++/13/ctime \
  /usr/include/c++/13/cwchar \
  /usr/include/c++/13/cwctype \
  /usr/include/c++/13/debug/assertions.h \
  /usr/include/c++/13/debug/debug.h \
  /usr/include/c++/13/deque \
  /usr/include/c++/13/exception \
  /usr/include/c++/13/ext/aligned_buffer.h \
  /usr/include/c++/13/ext/alloc_traits.h \
  /usr/include/c++/13/ext/atomicity.h \
  /usr/include/c++/13/ext/concurrence.h \
  /usr/include/c++/13/ext/numeric_traits.h \
  /usr/include/c++/13/ext/string_conversions.h \
  /usr/include/c++/13/ext/type_traits.h \
  /usr/include/c++/13/functional \
  /usr/include/c++/13/initializer_list \
  /usr/include/c++/13/iomanip \
  /usr/include/c++/13/ios \
  /usr/include/c++/13/iosfwd \
  /usr/include/c++/13/iostream \
  /usr/include/c++/13/istream \
  /usr/include/c++/13/limits \
  /usr/include/c++/13/locale \
  /usr/include/c++/13/math.h \
  /usr/include/c++/13/memory \
  /usr/include/c++/13/mutex \
  /usr/include/c++/13/new \
  /usr/include/c++/13/ostream \
  /usr/include/c++/13/pstl/execution_defs.h \
  /usr/include/c++/13/pstl/glue_memory_defs.h \
  /usr/include/c++/13/pstl/pstl_config.h \
  /usr/include/c++/13/queue \
  /usr/include/c++/13/ratio \
  /usr/include/c++/13/sstream \
  /usr/include/c++/13/stdexcept \
  /usr/include/c++/13/stdlib.h \
  /usr/include/c++/13/streambuf \
  /usr/include/c++/13/string \
  /usr/include/c++/13/string_view \
  /usr/include/c++/13/system_error \
  /usr/include/c++/13/thread \
  /usr/include/c++/13/tr1/bessel_function.tcc \
  /usr/include/c++/13/tr1/beta_function.tcc \
  /usr/include/c++/13/tr1/ell_integral.tcc \
  /usr/include/c++/13/tr1/exp_integral.tcc \
  /usr/include/c++/13/tr1/gamma.tcc \
  /usr/include/c++/13/tr1/hypergeometric.tcc \
  /usr/include/c++/13/tr1/legendre_function.tcc \
  /usr/include/c++/13/tr1/modified_bessel_func.tcc \
  /usr/include/c++/13/tr1/poly_hermite.tcc \
  /usr/include/c++/13/tr1/poly_laguerre.tcc \
  /usr/include/c++/13/tr1/riemann_zeta.tcc \
  /usr/include/c++/13/tr1/special_function_util.h \
  /usr/include/c++/13/tuple \
  /usr/include/c++/13/type_traits \
  /usr/include/c++/13/typeinfo \
  /usr/include/c++/13/unordered_map \
  /usr/include/c++/13/vector \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/inttypes.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/signal.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/include/x86_64-linux-gnu/asm/errno.h \
  /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
  /usr/include/x86_64-linux-gnu/bits/byteswap.h \
  /usr/include/x86_64-linux-gnu/bits/confname.h \
  /usr/include/x86_64-linux-gnu/bits/cpu-set.h \
  /usr/include/x86_64-linux-gnu/bits/endian.h \
  /usr/include/x86_64-linux-gnu/bits/endianness.h \
  /usr/include/x86_64-linux-gnu/bits/environments.h \
  /usr/include/x86_64-linux-gnu/bits/errno.h \
  /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
  /usr/include/x86_64-linux-gnu/bits/floatn.h \
  /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
  /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
  /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
  /usr/include/x86_64-linux-gnu/bits/getopt_core.h \
  /usr/include/x86_64-linux-gnu/bits/getopt_posix.h \
  /usr/include/x86_64-linux-gnu/bits/iscanonical.h \
  /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
  /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
  /usr/include/x86_64-linux-gnu/bits/local_lim.h \
  /usr/include/x86_64-linux-gnu/bits/locale.h \
  /usr/include/x86_64-linux-gnu/bits/long-double.h \
  /usr/include/x86_64-linux-gnu/bits/math-vector.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
  /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
  /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
  /usr/include/x86_64-linux-gnu/bits/posix_opt.h \
  /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
  /usr/include/x86_64-linux-gnu/bits/sched.h \
  /usr/include/x86_64-linux-gnu/bits/select-decl.h \
  /usr/include/x86_64-linux-gnu/bits/select.h \
  /usr/include/x86_64-linux-gnu/bits/select2.h \
  /usr/include/x86_64-linux-gnu/bits/setjmp.h \
  /usr/include/x86_64-linux-gnu/bits/sigaction.h \
  /usr/include/x86_64-linux-gnu/bits/sigcontext.h \
  /usr/include/x86_64-linux-gnu/bits/sigevent-consts.h \
  /usr/include/x86_64-linux-gnu/bits/siginfo-arch.h \
  /usr/include/x86_64-linux-gnu/bits/siginfo-consts-arch.h \
  /usr/include/x86_64-linux-gnu/bits/siginfo-consts.h \
  /usr/include/x86_64-linux-gnu/bits/signal_ext.h \
  /usr/include/x86_64-linux-gnu/bits/signum-arch.h \
  /usr/include/x86_64-linux-gnu/bits/signum-generic.h \
  /usr/include/x86_64-linux-gnu/bits/sigstack.h \
  /usr/include/x86_64-linux-gnu/bits/sigstksz.h \
  /usr/include/x86_64-linux-gnu/bits/sigthread.h \
  /usr/include/x86_64-linux-gnu/bits/ss_flags.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-least.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
  /usr/include/x86_64-linux-gnu/bits/stdio.h \
  /usr/include/x86_64-linux-gnu/bits/stdio2-decl.h \
  /usr/include/x86_64-linux-gnu/bits/stdio2.h \
  /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib.h \
  /usr/include/x86_64-linux-gnu/bits/string_fortified.h \
  /usr/include/x86_64-linux-gnu/bits/strings_fortified.h \
  /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
  /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h \
  /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
  /usr/include/x86_64-linux-gnu/bits/time.h \
  /usr/include/x86_64-linux-gnu/bits/time64.h \
  /usr/include/x86_64-linux-gnu/bits/timesize.h \
  /usr/include/x86_64-linux-gnu/bits/timex.h \
  /usr/include/x86_64-linux-gnu/bits/types.h \
  /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/error_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigval_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/stack_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
  /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
  /usr/include/x86_64-linux-gnu/bits/typesizes.h \
  /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
  /usr/include/x86_64-linux-gnu/bits/uio_lim.h \
  /usr/include/x86_64-linux-gnu/bits/unistd-decl.h \
  /usr/include/x86_64-linux-gnu/bits/unistd.h \
  /usr/include/x86_64-linux-gnu/bits/unistd_ext.h \
  /usr/include/x86_64-linux-gnu/bits/waitflags.h \
  /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
  /usr/include/x86_64-linux-gnu/bits/wchar.h \
  /usr/include/x86_64-linux-gnu/bits/wchar2-decl.h \
  /usr/include/x86_64-linux-gnu/bits/wchar2.h \
  /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h \
  /usr/include/x86_64-linux-gnu/bits/wordsize.h \
  /usr/include/x86_64-linux-gnu/bits/xopen_lim.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/atomic_word.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/c++allocator.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/c++config.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/c++locale.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/cpu_defines.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/ctype_base.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/ctype_inline.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/error_constants.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/gthr-default.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/gthr.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/messages_members.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/os_defines.h \
  /usr/include/x86_64-linux-gnu/c++/13/bits/time_members.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs.h \
  /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h \
  /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h \
  /usr/include/x86_64-linux-gnu/libavcodec/defs.h \
  /usr/include/x86_64-linux-gnu/libavcodec/packet.h \
  /usr/include/x86_64-linux-gnu/libavcodec/version.h \
  /usr/include/x86_64-linux-gnu/libavcodec/version_major.h \
  /usr/include/x86_64-linux-gnu/libavdevice/avdevice.h \
  /usr/include/x86_64-linux-gnu/libavdevice/version.h \
  /usr/include/x86_64-linux-gnu/libavdevice/version_major.h \
  /usr/include/x86_64-linux-gnu/libavformat/avformat.h \
  /usr/include/x86_64-linux-gnu/libavformat/avio.h \
  /usr/include/x86_64-linux-gnu/libavformat/version.h \
  /usr/include/x86_64-linux-gnu/libavformat/version_major.h \
  /usr/include/x86_64-linux-gnu/libavutil/attributes.h \
  /usr/include/x86_64-linux-gnu/libavutil/avconfig.h \
  /usr/include/x86_64-linux-gnu/libavutil/avutil.h \
  /usr/include/x86_64-linux-gnu/libavutil/buffer.h \
  /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h \
  /usr/include/x86_64-linux-gnu/libavutil/common.h \
  /usr/include/x86_64-linux-gnu/libavutil/dict.h \
  /usr/include/x86_64-linux-gnu/libavutil/error.h \
  /usr/include/x86_64-linux-gnu/libavutil/frame.h \
  /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h \
  /usr/include/x86_64-linux-gnu/libavutil/imgutils.h \
  /usr/include/x86_64-linux-gnu/libavutil/intfloat.h \
  /usr/include/x86_64-linux-gnu/libavutil/log.h \
  /usr/include/x86_64-linux-gnu/libavutil/macros.h \
  /usr/include/x86_64-linux-gnu/libavutil/mathematics.h \
  /usr/include/x86_64-linux-gnu/libavutil/mem.h \
  /usr/include/x86_64-linux-gnu/libavutil/opt.h \
  /usr/include/x86_64-linux-gnu/libavutil/pixdesc.h \
  /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h \
  /usr/include/x86_64-linux-gnu/libavutil/rational.h \
  /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h \
  /usr/include/x86_64-linux-gnu/libavutil/time.h \
  /usr/include/x86_64-linux-gnu/libavutil/version.h \
  /usr/include/x86_64-linux-gnu/libswscale/swscale.h \
  /usr/include/x86_64-linux-gnu/libswscale/version.h \
  /usr/include/x86_64-linux-gnu/libswscale/version_major.h \
  /usr/include/x86_64-linux-gnu/sys/cdefs.h \
  /usr/include/x86_64-linux-gnu/sys/select.h \
  /usr/include/x86_64-linux-gnu/sys/single_threaded.h \
  /usr/include/x86_64-linux-gnu/sys/types.h \
  /usr/include/x86_64-linux-gnu/sys/ucontext.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/limits.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/stdarg.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/stddef.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/stdint.h \
  /usr/lib/gcc/x86_64-linux-gnu/13/include/syslimits.h


/usr/include/x86_64-linux-gnu/bits/unistd_ext.h:

/usr/include/x86_64-linux-gnu/bits/unistd.h:

/usr/include/x86_64-linux-gnu/bits/unistd-decl.h:

/usr/include/x86_64-linux-gnu/bits/types/stack_t.h:

/usr/include/x86_64-linux-gnu/bits/types/sigval_t.h:

/usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h:

/usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h:

/usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h:

/usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h:

/usr/include/x86_64-linux-gnu/bits/ss_flags.h:

/usr/include/x86_64-linux-gnu/bits/sigthread.h:

/usr/include/x86_64-linux-gnu/bits/sigstksz.h:

/usr/include/x86_64-linux-gnu/bits/sigstack.h:

/usr/include/x86_64-linux-gnu/bits/siginfo-consts-arch.h:

/usr/include/x86_64-linux-gnu/bits/siginfo-arch.h:

/usr/include/x86_64-linux-gnu/bits/sigevent-consts.h:

/usr/include/x86_64-linux-gnu/bits/posix_opt.h:

/usr/include/x86_64-linux-gnu/bits/getopt_core.h:

/usr/include/linux/close_range.h:

/home/<USER>/Documents/ffmpeg/RecordTool/src/StreamPublisher.cpp:

/usr/include/c++/13/cstring:

/home/<USER>/Documents/ffmpeg/RecordTool/src/ScreenRecorder.cpp:

/usr/lib/gcc/x86_64-linux-gnu/13/include/stdint.h:

/usr/include/x86_64-linux-gnu/bits/environments.h:

/usr/lib/gcc/x86_64-linux-gnu/13/include/stddef.h:

/usr/include/x86_64-linux-gnu/bits/siginfo-consts.h:

/usr/lib/gcc/x86_64-linux-gnu/13/include/stdarg.h:

/usr/include/x86_64-linux-gnu/sys/types.h:

/usr/include/x86_64-linux-gnu/sys/single_threaded.h:

/usr/include/x86_64-linux-gnu/sys/cdefs.h:

/usr/include/x86_64-linux-gnu/libswscale/version_major.h:

/usr/include/x86_64-linux-gnu/libavutil/version.h:

/usr/include/x86_64-linux-gnu/libavutil/time.h:

/usr/include/x86_64-linux-gnu/libavutil/samplefmt.h:

/usr/include/x86_64-linux-gnu/libavutil/rational.h:

/usr/include/x86_64-linux-gnu/libavutil/pixfmt.h:

/usr/include/x86_64-linux-gnu/libavutil/macros.h:

/usr/include/x86_64-linux-gnu/libavutil/intfloat.h:

/usr/include/x86_64-linux-gnu/libavutil/imgutils.h:

/usr/include/unistd.h:

/usr/include/x86_64-linux-gnu/libavutil/hwcontext.h:

/usr/include/x86_64-linux-gnu/libavutil/frame.h:

/usr/include/x86_64-linux-gnu/libavutil/buffer.h:

/usr/include/x86_64-linux-gnu/libavutil/avconfig.h:

/usr/include/x86_64-linux-gnu/libavutil/attributes.h:

/usr/include/x86_64-linux-gnu/libavformat/version.h:

/usr/include/x86_64-linux-gnu/libavcodec/version.h:

/usr/include/x86_64-linux-gnu/sys/ucontext.h:

/usr/include/x86_64-linux-gnu/libavcodec/defs.h:

/usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h:

/usr/include/x86_64-linux-gnu/libavcodec/avcodec.h:

/usr/include/x86_64-linux-gnu/gnu/stubs.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/time_members.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/os_defines.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/messages_members.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/gthr.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/error_constants.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/ctype_base.h:

/usr/include/x86_64-linux-gnu/libswscale/swscale.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/cpu_defines.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/c++locale.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/c++config.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/c++allocator.h:

/usr/include/x86_64-linux-gnu/bits/xopen_lim.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/atomic_word.h:

/usr/include/x86_64-linux-gnu/bits/wordsize.h:

/usr/include/x86_64-linux-gnu/bits/wctype-wchar.h:

/usr/include/x86_64-linux-gnu/bits/wchar.h:

/usr/include/x86_64-linux-gnu/bits/waitflags.h:

/usr/include/x86_64-linux-gnu/bits/uio_lim.h:

/usr/include/x86_64-linux-gnu/bits/uintn-identity.h:

/usr/include/x86_64-linux-gnu/bits/typesizes.h:

/usr/include/x86_64-linux-gnu/bits/types/timer_t.h:

/usr/include/x86_64-linux-gnu/bits/types/time_t.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h:

/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h:

/usr/include/x86_64-linux-gnu/bits/types/locale_t.h:

/usr/lib/gcc/x86_64-linux-gnu/13/include/syslimits.h:

/usr/include/x86_64-linux-gnu/bits/types/clock_t.h:

/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h:

/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h:

/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h:

/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h:

/usr/include/x86_64-linux-gnu/bits/types/FILE.h:

/usr/include/c++/13/debug/assertions.h:

/usr/include/x86_64-linux-gnu/bits/select-decl.h:

/usr/include/c++/13/ctime:

/usr/include/c++/13/codecvt:

/usr/include/x86_64-linux-gnu/libavdevice/avdevice.h:

/usr/include/c++/13/cstdio:

/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h:

/usr/include/c++/13/bits/charconv.h:

/usr/include/c++/13/bits/fs_path.h:

/usr/include/c++/13/clocale:

/usr/include/c++/13/cerrno:

/usr/include/x86_64-linux-gnu/bits/stdint-least.h:

/usr/include/x86_64-linux-gnu/bits/strings_fortified.h:

/usr/include/c++/13/cstdint:

/usr/include/c++/13/cctype:

/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h:

/usr/include/c++/13/ext/atomicity.h:

/usr/include/c++/13/tr1/gamma.tcc:

/usr/include/x86_64-linux-gnu/bits/posix1_lim.h:

/usr/include/x86_64-linux-gnu/libswscale/version.h:

/usr/include/c++/13/bits/vector.tcc:

/usr/include/c++/13/bits/stl_function.h:

/usr/include/c++/13/tr1/beta_function.tcc:

/usr/include/c++/13/bits/uses_allocator_args.h:

/usr/include/c++/13/bits/atomic_base.h:

/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h:

/usr/include/c++/13/bits/unique_ptr.h:

/usr/include/c++/13/bits/align.h:

/usr/include/c++/13/bits/unordered_map.h:

/usr/include/c++/13/bits/new_allocator.h:

/usr/include/c++/13/bits/stl_uninitialized.h:

/usr/include/c++/13/locale:

/usr/include/c++/13/bits/stl_raw_storage_iter.h:

/usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h:

/usr/include/c++/13/filesystem:

/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h:

/usr/include/asm-generic/errno.h:

/usr/include/c++/13/bits/functexcept.h:

/usr/include/x86_64-linux-gnu/bits/libc-header-start.h:

/usr/include/c++/13/bits/stl_construct.h:

/usr/include/x86_64-linux-gnu/libavcodec/version_major.h:

/usr/include/c++/13/bits/std_mutex.h:

/usr/include/c++/13/bits/stl_bvector.h:

/usr/include/c++/13/bits/stl_algobase.h:

/usr/include/c++/13/chrono:

/usr/include/c++/13/cstdlib:

/home/<USER>/Documents/ffmpeg/RecordTool/include/StreamPublisher.h:

/usr/include/c++/13/bits/stl_algo.h:

/usr/include/c++/13/cstddef:

/usr/include/c++/13/bits/uses_allocator.h:

/usr/include/x86_64-linux-gnu/libavformat/avformat.h:

/usr/include/c++/13/bits/postypes.h:

/usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h:

/usr/include/c++/13/bits/shared_ptr_atomic.h:

/usr/include/x86_64-linux-gnu/libavformat/avio.h:

/usr/include/x86_64-linux-gnu/bits/wchar2-decl.h:

/usr/include/c++/13/bits/erase_if.h:

/usr/include/c++/13/bits/stringfwd.h:

/usr/include/c++/13/bits/this_thread_sleep.h:

/usr/include/c++/13/bits/shared_ptr.h:

/usr/include/x86_64-linux-gnu/bits/struct_mutex.h:

/usr/include/c++/13/bits/stl_deque.h:

/usr/include/c++/13/bits/locale_facets.tcc:

/usr/include/x86_64-linux-gnu/libavcodec/codec_id.h:

/usr/include/c++/13/bits/refwrap.h:

/home/<USER>/Documents/ffmpeg/RecordTool/src/main.cpp:

/usr/include/c++/13/compare:

/usr/include/c++/13/system_error:

/usr/include/c++/13/bits/string_view.tcc:

/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h:

/usr/include/c++/13/bits/stl_heap.h:

/usr/include/c++/13/bits/std_abs.h:

/usr/include/c++/13/bits/parse_numbers.h:

/usr/include/c++/13/bits/ostream_insert.h:

/usr/include/x86_64-linux-gnu/libavutil/avutil.h:

/usr/include/c++/13/cwctype:

/home/<USER>/Documents/ffmpeg/RecordTool/include/RecordManager.h:

/usr/include/c++/13/bits/sstream.tcc:

/usr/include/c++/13/bits/move.h:

/usr/include/c++/13/ostream:

/usr/include/c++/13/tr1/riemann_zeta.tcc:

/usr/include/c++/13/bits/cpp_type_traits.h:

/usr/include/x86_64-linux-gnu/bits/types.h:

/usr/include/c++/13/bits/deque.tcc:

/usr/include/x86_64-linux-gnu/bits/endian.h:

/usr/include/c++/13/bits/predefined_ops.h:

/usr/include/x86_64-linux-gnu/bits/signum-generic.h:

/usr/include/c++/13/iosfwd:

/usr/include/c++/13/bits/stl_queue.h:

/usr/include/c++/13/bits/requires_hosted.h:

/usr/include/x86_64-linux-gnu/bits/floatn-common.h:

/usr/include/c++/13/bits/atomic_lockfree_defines.h:

/usr/include/x86_64-linux-gnu/asm/errno.h:

/usr/include/x86_64-linux-gnu/bits/stdlib.h:

/usr/include/c++/13/bits/streambuf.tcc:

/usr/include/c++/13/bits/specfun.h:

/usr/include/c++/13/bits/basic_string.h:

/usr/include/c++/13/tr1/poly_laguerre.tcc:

/usr/include/libintl.h:

/usr/include/c++/13/atomic:

/usr/include/c++/13/bits/locale_facets.h:

/usr/include/x86_64-linux-gnu/libavutil/opt.h:

/usr/include/c++/13/bits/stl_vector.h:

/usr/include/c++/13/bits/stl_tempbuf.h:

/usr/include/c++/13/bits/codecvt.h:

/usr/include/c++/13/bits/ostream.tcc:

/usr/include/x86_64-linux-gnu/bits/byteswap.h:

/usr/include/x86_64-linux-gnu/libavutil/mathematics.h:

/usr/include/c++/13/backward/binders.h:

/usr/include/c++/13/bits/basic_ios.tcc:

/usr/include/x86_64-linux-gnu/libavutil/error.h:

/usr/include/x86_64-linux-gnu/libavutil/common.h:

/usr/include/c++/13/pstl/execution_defs.h:

/usr/include/c++/13/bits/alloc_traits.h:

/usr/include/c++/13/bits/concept_check.h:

/usr/include/x86_64-linux-gnu/bits/long-double.h:

/usr/include/c++/13/bits/algorithmfwd.h:

/usr/include/x86_64-linux-gnu/bits/wchar2.h:

/usr/include/x86_64-linux-gnu/bits/fp-logb.h:

/usr/include/x86_64-linux-gnu/gnu/stubs-64.h:

/usr/include/c++/13/bits/node_handle.h:

/usr/include/c++/13/bits/exception_ptr.h:

/usr/include/c++/13/bits/locale_conv.h:

/usr/include/c++/13/bits/istream.tcc:

/usr/include/c++/13/math.h:

/home/<USER>/Documents/ffmpeg/RecordTool/src/RecordManager.cpp:

/usr/include/c++/13/bits/unique_lock.h:

/usr/include/c++/13/mutex:

/usr/include/c++/13/bits/allocated_ptr.h:

/usr/include/c++/13/bit:

/usr/include/c++/13/bits/basic_string.tcc:

/usr/include/x86_64-linux-gnu/bits/endianness.h:

/usr/include/c++/13/bits/chrono.h:

/usr/include/c++/13/bits/fs_ops.h:

/usr/include/x86_64-linux-gnu/bits/mathcalls.h:

/usr/include/x86_64-linux-gnu/bits/signal_ext.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/ctype_inline.h:

/usr/include/c++/13/bits/std_function.h:

/usr/include/c++/13/bits/char_traits.h:

/usr/include/c++/13/bits/hashtable_policy.h:

/usr/include/alloca.h:

/usr/include/x86_64-linux-gnu/sys/select.h:

/usr/include/c++/13/cwchar:

/home/<USER>/Documents/ffmpeg/RecordTool/include/ScreenRecorder.h:

/usr/include/c++/13/backward/auto_ptr.h:

/usr/include/wchar.h:

/usr/include/c++/13/bits/shared_ptr_base.h:

/usr/include/c++/13/bits/basic_ios.h:

/usr/include/x86_64-linux-gnu/bits/time64.h:

/usr/include/c++/13/bits/cxxabi_forced.h:

/usr/include/c++/13/bits/localefwd.h:

/usr/include/c++/13/bits/streambuf_iterator.h:

/usr/include/x86_64-linux-gnu/bits/flt-eval-method.h:

/usr/include/x86_64-linux-gnu/bits/waitstatus.h:

/usr/include/c++/13/bits/exception_defines.h:

/usr/include/x86_64-linux-gnu/bits/types/__FILE.h:

/usr/include/math.h:

/usr/include/x86_64-linux-gnu/libavutil/mem.h:

/usr/include/c++/13/bits/cxxabi_init_exception.h:

/usr/include/c++/13/typeinfo:

/usr/include/c++/13/bits/exception.h:

/usr/include/c++/13/bits/memory_resource.h:

/usr/include/c++/13/streambuf:

/usr/include/c++/13/condition_variable:

/usr/include/x86_64-linux-gnu/bits/stdlib-float.h:

/usr/include/x86_64-linux-gnu/bits/types/error_t.h:

/usr/include/c++/13/bits/hashtable.h:

/usr/include/x86_64-linux-gnu/bits/sigaction.h:

/usr/include/c++/13/bits/fs_fwd.h:

/usr/include/c++/13/bits/memoryfwd.h:

/usr/include/c++/13/bits/functional_hash.h:

/usr/include/errno.h:

/usr/include/c++/13/array:

/usr/include/x86_64-linux-gnu/libavutil/log.h:

/usr/include/c++/13/bits/stl_iterator_base_funcs.h:

/usr/include/asm-generic/errno-base.h:

/usr/include/c++/13/bits/hash_bytes.h:

/usr/include/x86_64-linux-gnu/bits/math-vector.h:

/usr/include/c++/13/bits/ios_base.h:

/usr/include/c++/13/bits/uniform_int_dist.h:

/usr/include/c++/13/bits/locale_classes.tcc:

/usr/include/c++/13/bits/fs_dir.h:

/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h:

/usr/include/c++/13/sstream:

/usr/include/x86_64-linux-gnu/bits/confname.h:

/usr/include/c++/13/bits/locale_facets_nonio.h:

/usr/include/c++/13/bits/locale_facets_nonio.tcc:

/usr/include/c++/13/bits/invoke.h:

/usr/include/x86_64-linux-gnu/bits/signum-arch.h:

/usr/include/c++/13/bits/stl_pair.h:

/usr/include/x86_64-linux-gnu/bits/errno.h:

/usr/include/c++/13/ext/aligned_buffer.h:

/usr/include/locale.h:

/usr/include/c++/13/bits/utility.h:

/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h:

/usr/include/x86_64-linux-gnu/libavdevice/version.h:

/usr/include/c++/13/ext/alloc_traits.h:

/usr/include/c++/13/new:

/usr/include/c++/13/deque:

/usr/include/inttypes.h:

/usr/include/c++/13/ext/concurrence.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h:

/usr/include/c++/13/bits/stl_iterator.h:

/usr/include/c++/13/ext/numeric_traits.h:

/usr/include/c++/13/ext/string_conversions.h:

/usr/include/c++/13/ext/type_traits.h:

/usr/include/c++/13/functional:

/usr/include/x86_64-linux-gnu/libavutil/dict.h:

/usr/include/c++/13/ios:

/usr/include/c++/13/iostream:

/usr/include/c++/13/istream:

/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h:

/usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h:

/usr/include/x86_64-linux-gnu/bits/types/wint_t.h:

/usr/include/c++/13/limits:

/usr/include/c++/13/memory:

/usr/include/x86_64-linux-gnu/bits/floatn.h:

/usr/include/c++/13/exception:

/usr/include/c++/13/pstl/glue_memory_defs.h:

/usr/include/signal.h:

/usr/include/c++/13/iomanip:

/usr/include/strings.h:

/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h:

/usr/include/c++/13/pstl/pstl_config.h:

/usr/include/x86_64-linux-gnu/libavformat/version_major.h:

/usr/include/c++/13/queue:

/usr/include/c++/13/ratio:

/usr/include/linux/limits.h:

/usr/include/endian.h:

/usr/include/stdint.h:

/usr/include/c++/13/bits/enable_special_members.h:

/usr/include/x86_64-linux-gnu/bits/posix2_lim.h:

/usr/include/c++/13/stdexcept:

/usr/include/c++/13/stdlib.h:

/usr/include/x86_64-linux-gnu/bits/stdint-intn.h:

/usr/include/c++/13/string:

/usr/include/features-time64.h:

/usr/include/x86_64-linux-gnu/libavcodec/codec_par.h:

/usr/include/c++/13/thread:

/usr/include/c++/13/tr1/ell_integral.tcc:

/usr/include/c++/13/tr1/exp_integral.tcc:

/usr/include/c++/13/bits/std_thread.h:

/usr/include/c++/13/tr1/bessel_function.tcc:

/usr/include/c++/13/initializer_list:

/usr/include/limits.h:

/usr/include/c++/13/bits/range_access.h:

/usr/include/c++/13/tr1/hypergeometric.tcc:

/usr/include/c++/13/type_traits:

/usr/include/c++/13/tr1/legendre_function.tcc:

/usr/lib/gcc/x86_64-linux-gnu/13/include/limits.h:

/usr/include/c++/13/tr1/modified_bessel_func.tcc:

/usr/include/x86_64-linux-gnu/libavutil/pixdesc.h:

/usr/include/c++/13/tr1/poly_hermite.tcc:

/usr/include/x86_64-linux-gnu/bits/sigcontext.h:

/usr/include/c++/13/tr1/special_function_util.h:

/usr/include/stdc-predef.h:

/usr/include/c++/13/bits/nested_exception.h:

/usr/include/c++/13/tuple:

/usr/include/c++/13/bits/allocator.h:

/usr/include/c++/13/unordered_map:

/usr/include/c++/13/vector:

/usr/include/ctype.h:

/usr/include/features.h:

/usr/include/c++/13/debug/debug.h:

/usr/include/linux/errno.h:

/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h:

/usr/include/pthread.h:

/usr/include/wctype.h:

/usr/include/sched.h:

/usr/include/x86_64-linux-gnu/libavcodec/packet.h:

/usr/include/c++/13/bits/stl_iterator_base_types.h:

/usr/include/stdio.h:

/usr/include/stdlib.h:

/usr/include/x86_64-linux-gnu/libavutil/channel_layout.h:

/usr/include/string.h:

/usr/include/x86_64-linux-gnu/bits/stdio.h:

/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h:

/usr/include/x86_64-linux-gnu/bits/cpu-set.h:

/usr/include/x86_64-linux-gnu/libavdevice/version_major.h:

/usr/include/x86_64-linux-gnu/bits/fp-fast.h:

/usr/include/c++/13/string_view:

/usr/include/x86_64-linux-gnu/bits/iscanonical.h:

/usr/include/x86_64-linux-gnu/bits/local_lim.h:

/usr/include/x86_64-linux-gnu/bits/locale.h:

/usr/include/x86_64-linux-gnu/libavcodec/codec.h:

/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h:

/usr/include/x86_64-linux-gnu/bits/sched.h:

/usr/include/c++/13/cmath:

/usr/include/x86_64-linux-gnu/bits/select2.h:

/usr/include/x86_64-linux-gnu/bits/getopt_posix.h:

/usr/include/time.h:

/usr/include/x86_64-linux-gnu/bits/setjmp.h:

/usr/include/x86_64-linux-gnu/c++/13/bits/gthr-default.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h:

/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h:

/usr/include/c++/13/bits/ptr_traits.h:

/usr/include/x86_64-linux-gnu/bits/stdio2-decl.h:

/usr/include/c++/13/bits/quoted_string.h:

/usr/include/x86_64-linux-gnu/bits/stdio2.h:

/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h:

/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h:

/usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h:

/usr/include/x86_64-linux-gnu/bits/string_fortified.h:

/usr/include/x86_64-linux-gnu/bits/select.h:

/usr/include/x86_64-linux-gnu/bits/time.h:

/usr/include/c++/13/bits/locale_classes.h:

/usr/include/x86_64-linux-gnu/bits/timesize.h:

/usr/include/x86_64-linux-gnu/bits/stdio_lim.h:

/usr/include/x86_64-linux-gnu/bits/timex.h:
