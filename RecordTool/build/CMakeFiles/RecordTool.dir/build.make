# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/ffmpeg/RecordTool

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/ffmpeg/RecordTool/build

# Include any dependencies generated for this target.
include CMakeFiles/RecordTool.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/RecordTool.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/RecordTool.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/RecordTool.dir/flags.make

CMakeFiles/RecordTool.dir/src/main.cpp.o: CMakeFiles/RecordTool.dir/flags.make
CMakeFiles/RecordTool.dir/src/main.cpp.o: /home/<USER>/Documents/ffmpeg/RecordTool/src/main.cpp
CMakeFiles/RecordTool.dir/src/main.cpp.o: CMakeFiles/RecordTool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/ffmpeg/RecordTool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/RecordTool.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RecordTool.dir/src/main.cpp.o -MF CMakeFiles/RecordTool.dir/src/main.cpp.o.d -o CMakeFiles/RecordTool.dir/src/main.cpp.o -c /home/<USER>/Documents/ffmpeg/RecordTool/src/main.cpp

CMakeFiles/RecordTool.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RecordTool.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/ffmpeg/RecordTool/src/main.cpp > CMakeFiles/RecordTool.dir/src/main.cpp.i

CMakeFiles/RecordTool.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RecordTool.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/ffmpeg/RecordTool/src/main.cpp -o CMakeFiles/RecordTool.dir/src/main.cpp.s

CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o: CMakeFiles/RecordTool.dir/flags.make
CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o: /home/<USER>/Documents/ffmpeg/RecordTool/src/ScreenRecorder.cpp
CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o: CMakeFiles/RecordTool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/ffmpeg/RecordTool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o -MF CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o.d -o CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o -c /home/<USER>/Documents/ffmpeg/RecordTool/src/ScreenRecorder.cpp

CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/ffmpeg/RecordTool/src/ScreenRecorder.cpp > CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.i

CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/ffmpeg/RecordTool/src/ScreenRecorder.cpp -o CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.s

CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o: CMakeFiles/RecordTool.dir/flags.make
CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o: /home/<USER>/Documents/ffmpeg/RecordTool/src/StreamPublisher.cpp
CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o: CMakeFiles/RecordTool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/ffmpeg/RecordTool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o -MF CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o.d -o CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o -c /home/<USER>/Documents/ffmpeg/RecordTool/src/StreamPublisher.cpp

CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/ffmpeg/RecordTool/src/StreamPublisher.cpp > CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.i

CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/ffmpeg/RecordTool/src/StreamPublisher.cpp -o CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.s

CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o: CMakeFiles/RecordTool.dir/flags.make
CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o: /home/<USER>/Documents/ffmpeg/RecordTool/src/RecordManager.cpp
CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o: CMakeFiles/RecordTool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/ffmpeg/RecordTool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o -MF CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o.d -o CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o -c /home/<USER>/Documents/ffmpeg/RecordTool/src/RecordManager.cpp

CMakeFiles/RecordTool.dir/src/RecordManager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RecordTool.dir/src/RecordManager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/ffmpeg/RecordTool/src/RecordManager.cpp > CMakeFiles/RecordTool.dir/src/RecordManager.cpp.i

CMakeFiles/RecordTool.dir/src/RecordManager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RecordTool.dir/src/RecordManager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/ffmpeg/RecordTool/src/RecordManager.cpp -o CMakeFiles/RecordTool.dir/src/RecordManager.cpp.s

# Object files for target RecordTool
RecordTool_OBJECTS = \
"CMakeFiles/RecordTool.dir/src/main.cpp.o" \
"CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o" \
"CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o" \
"CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o"

# External object files for target RecordTool
RecordTool_EXTERNAL_OBJECTS =

RecordTool: CMakeFiles/RecordTool.dir/src/main.cpp.o
RecordTool: CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o
RecordTool: CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o
RecordTool: CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o
RecordTool: CMakeFiles/RecordTool.dir/build.make
RecordTool: CMakeFiles/RecordTool.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Documents/ffmpeg/RecordTool/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX executable RecordTool"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/RecordTool.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/RecordTool.dir/build: RecordTool
.PHONY : CMakeFiles/RecordTool.dir/build

CMakeFiles/RecordTool.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/RecordTool.dir/cmake_clean.cmake
.PHONY : CMakeFiles/RecordTool.dir/clean

CMakeFiles/RecordTool.dir/depend:
	cd /home/<USER>/Documents/ffmpeg/RecordTool/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/ffmpeg/RecordTool /home/<USER>/Documents/ffmpeg/RecordTool /home/<USER>/Documents/ffmpeg/RecordTool/build /home/<USER>/Documents/ffmpeg/RecordTool/build /home/<USER>/Documents/ffmpeg/RecordTool/build/CMakeFiles/RecordTool.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/RecordTool.dir/depend

