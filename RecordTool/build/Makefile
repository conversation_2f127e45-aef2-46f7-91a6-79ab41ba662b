# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/ffmpeg/RecordTool

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/ffmpeg/RecordTool/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/ffmpeg/RecordTool/build/CMakeFiles /home/<USER>/Documents/ffmpeg/RecordTool/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/ffmpeg/RecordTool/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named RecordTool

# Build rule for target.
RecordTool: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 RecordTool
.PHONY : RecordTool

# fast build rule for target.
RecordTool/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/build
.PHONY : RecordTool/fast

src/RecordManager.o: src/RecordManager.cpp.o
.PHONY : src/RecordManager.o

# target to build an object file
src/RecordManager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/src/RecordManager.cpp.o
.PHONY : src/RecordManager.cpp.o

src/RecordManager.i: src/RecordManager.cpp.i
.PHONY : src/RecordManager.i

# target to preprocess a source file
src/RecordManager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/src/RecordManager.cpp.i
.PHONY : src/RecordManager.cpp.i

src/RecordManager.s: src/RecordManager.cpp.s
.PHONY : src/RecordManager.s

# target to generate assembly for a file
src/RecordManager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/src/RecordManager.cpp.s
.PHONY : src/RecordManager.cpp.s

src/ScreenRecorder.o: src/ScreenRecorder.cpp.o
.PHONY : src/ScreenRecorder.o

# target to build an object file
src/ScreenRecorder.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.o
.PHONY : src/ScreenRecorder.cpp.o

src/ScreenRecorder.i: src/ScreenRecorder.cpp.i
.PHONY : src/ScreenRecorder.i

# target to preprocess a source file
src/ScreenRecorder.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.i
.PHONY : src/ScreenRecorder.cpp.i

src/ScreenRecorder.s: src/ScreenRecorder.cpp.s
.PHONY : src/ScreenRecorder.s

# target to generate assembly for a file
src/ScreenRecorder.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/src/ScreenRecorder.cpp.s
.PHONY : src/ScreenRecorder.cpp.s

src/StreamPublisher.o: src/StreamPublisher.cpp.o
.PHONY : src/StreamPublisher.o

# target to build an object file
src/StreamPublisher.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.o
.PHONY : src/StreamPublisher.cpp.o

src/StreamPublisher.i: src/StreamPublisher.cpp.i
.PHONY : src/StreamPublisher.i

# target to preprocess a source file
src/StreamPublisher.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.i
.PHONY : src/StreamPublisher.cpp.i

src/StreamPublisher.s: src/StreamPublisher.cpp.s
.PHONY : src/StreamPublisher.s

# target to generate assembly for a file
src/StreamPublisher.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/src/StreamPublisher.cpp.s
.PHONY : src/StreamPublisher.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RecordTool.dir/build.make CMakeFiles/RecordTool.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... RecordTool"
	@echo "... src/RecordManager.o"
	@echo "... src/RecordManager.i"
	@echo "... src/RecordManager.s"
	@echo "... src/ScreenRecorder.o"
	@echo "... src/ScreenRecorder.i"
	@echo "... src/ScreenRecorder.s"
	@echo "... src/StreamPublisher.o"
	@echo "... src/StreamPublisher.i"
	@echo "... src/StreamPublisher.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

