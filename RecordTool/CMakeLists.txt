cmake_minimum_required(VERSION 3.16)
project(RecordTool)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")

# 查找FFmpeg库
find_package(PkgConfig REQUIRED)
pkg_check_modules(FFMPEG REQUIRED
    libavcodec
    libavformat
    libavutil
    libavdevice
    libavfilter
    libswscale
    libswresample
)

# 查找其他依赖库
find_package(Threads REQUIRED)

# 包含头文件目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${FFMPEG_INCLUDE_DIRS})

# 设置源文件
set(SOURCES
    src/main.cpp
    src/ScreenRecorder.cpp
    src/StreamPublisher.cpp
    src/RecordManager.cpp
)

# 设置头文件
set(HEADERS
    include/ScreenRecorder.h
    include/StreamPublisher.h
    include/RecordManager.h
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# 链接库
target_link_libraries(${PROJECT_NAME}
    ${FFMPEG_LIBRARIES}
    Threads::Threads
    -lX11
    -lXext
    -lXfixes
)

# 设置链接目录
target_link_directories(${PROJECT_NAME} PRIVATE ${FFMPEG_LIBRARY_DIRS})

# 设置编译定义
target_compile_definitions(${PROJECT_NAME} PRIVATE ${FFMPEG_CFLAGS_OTHER})

# 安装规则
install(TARGETS ${PROJECT_NAME} DESTINATION bin)

# 打印配置信息
message(STATUS "FFmpeg libraries: ${FFMPEG_LIBRARIES}")
message(STATUS "FFmpeg include dirs: ${FFMPEG_INCLUDE_DIRS}")