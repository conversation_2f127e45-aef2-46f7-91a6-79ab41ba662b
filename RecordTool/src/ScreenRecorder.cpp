#include "ScreenRecorder.h"
#include <iostream>
#include <cstring>

// 辅助函数：获取FFmpeg错误字符串
static std::string av_error_string(int error_code) {
    char errbuf[AV_ERROR_MAX_STRING_SIZE];
    av_strerror(error_code, errbuf, AV_ERROR_MAX_STRING_SIZE);
    return std::string(errbuf);
}

ScreenRecorder::ScreenRecorder(const RecordConfig& config)
    : config_(config)
    , input_format_ctx_(nullptr)
    , output_format_ctx_(nullptr)
    , codec_ctx_(nullptr)
    , video_stream_(nullptr)
    , sws_ctx_(nullptr)
    , input_frame_(nullptr)
    , output_frame_(nullptr)
    , packet_(nullptr)
    , recording_(false)
    , initialized_(false)
    , start_time_(0)
    , frame_count_(0) {
}

ScreenRecorder::~ScreenRecorder() {
    stopRecording();
    cleanup();
}

bool ScreenRecorder::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);

    if (initialized_.load()) {
        return true;
    }

    // 注册所有设备和格式
    avdevice_register_all();

    // 初始化输入设备
    if (!initializeInput()) {
        std::cerr << "初始化输入设备失败" << std::endl;
        return false;
    }

    // 初始化编码器
    if (!initializeEncoder()) {
        std::cerr << "初始化编码器失败" << std::endl;
        cleanup();
        return false;
    }

    // 分配帧和数据包
    input_frame_ = av_frame_alloc();
    output_frame_ = av_frame_alloc();
    packet_ = av_packet_alloc();

    if (!input_frame_ || !output_frame_ || !packet_) {
        std::cerr << "分配帧或数据包失败" << std::endl;
        cleanup();
        return false;
    }

    // 设置输出帧参数
    output_frame_->format = codec_ctx_->pix_fmt;
    output_frame_->width = config_.width;
    output_frame_->height = config_.height;

    // 分配输出帧缓冲区
    int ret = av_frame_get_buffer(output_frame_, 32);
    if (ret < 0) {
        std::cerr << "分配输出帧缓冲区失败: " << av_error_string(ret) << std::endl;
        cleanup();
        return false;
    }

    initialized_.store(true);
    std::cout << "屏幕录制器初始化成功" << std::endl;
    return true;
}

bool ScreenRecorder::startRecording(const std::string& output_file) {
    if (!initialized_.load()) {
        std::cerr << "录制器未初始化" << std::endl;
        return false;
    }

    if (recording_.load()) {
        std::cerr << "已经在录制中" << std::endl;
        return false;
    }

    // 初始化输出文件
    if (!initializeOutput(output_file)) {
        std::cerr << "初始化输出文件失败: " << output_file << std::endl;
        return false;
    }

    // 写入文件头
    int ret = avformat_write_header(output_format_ctx_, nullptr);
    if (ret < 0) {
        std::cerr << "写入文件头失败: " << av_error_string(ret) << std::endl;
        return false;
    }

    recording_.store(true);
    start_time_ = av_gettime();
    frame_count_ = 0;

    // 启动录制线程
    record_thread_ = std::thread(&ScreenRecorder::recordingThread, this);

    std::cout << "开始录制到文件: " << output_file << std::endl;
    return true;
}

void ScreenRecorder::stopRecording() {
    if (!recording_.load()) {
        return;
    }

    recording_.store(false);

    // 等待录制线程结束
    if (record_thread_.joinable()) {
        record_thread_.join();
    }

    // 写入文件尾
    if (output_format_ctx_) {
        av_write_trailer(output_format_ctx_);
        avio_closep(&output_format_ctx_->pb);
        avformat_free_context(output_format_ctx_);
        output_format_ctx_ = nullptr;
    }

    std::cout << "录制已停止" << std::endl;
}

bool ScreenRecorder::captureFrame(AVFrame*& frame) {
    if (!initialized_.load()) {
        return false;
    }

    // 简化版本：直接使用输出帧进行模拟
    // 在实际应用中，这里应该从X11捕获屏幕数据

    // 设置时间戳
    output_frame_->pts = frame_count_++;

    // 这里应该填充实际的屏幕数据
    // 为了演示，我们创建一个简单的测试图像
    for (int y = 0; y < config_.height; y++) {
        for (int x = 0; x < config_.width; x++) {
            // 创建一个简单的渐变图像作为测试
            int luma = (x + y + frame_count_) % 256;
            output_frame_->data[0][y * output_frame_->linesize[0] + x] = luma;
        }
    }

    // 设置色度分量
    for (int y = 0; y < config_.height / 2; y++) {
        for (int x = 0; x < config_.width / 2; x++) {
            output_frame_->data[1][y * output_frame_->linesize[1] + x] = 128;
            output_frame_->data[2][y * output_frame_->linesize[2] + x] = 128;
        }
    }

    frame = output_frame_;
    return true;
}

double ScreenRecorder::getRecordingDuration() const {
    if (!recording_.load() || start_time_ == 0) {
        return 0.0;
    }

    int64_t current_time = av_gettime();
    return (current_time - start_time_) / 1000000.0; // 转换为秒
}

void ScreenRecorder::recordingThread() {
    std::cout << "录制线程已启动" << std::endl;

    while (recording_.load()) {
        AVFrame* frame = nullptr;
        if (captureFrame(frame) && frame) {
            if (!encodeAndWriteFrame(frame)) {
                std::cerr << "编码和写入帧失败" << std::endl;
                break;
            }
        }

        // 控制帧率
        std::this_thread::sleep_for(std::chrono::milliseconds(1000 / config_.fps));
    }

    std::cout << "录制线程已结束" << std::endl;
}

bool ScreenRecorder::initializeInput() {
    // 简化版本：不使用实际的X11捕获
    // 在实际应用中，这里应该初始化X11屏幕捕获
    std::cout << "初始化输入设备（模拟模式）" << std::endl;
    return true;
}

bool ScreenRecorder::initializeOutput(const std::string& output_file) {
    // 分配输出格式上下文
    int ret = avformat_alloc_output_context2(&output_format_ctx_, nullptr, nullptr, output_file.c_str());
    if (ret < 0) {
        std::cerr << "分配输出格式上下文失败: " << av_error_string(ret) << std::endl;
        return false;
    }

    // 创建视频流
    video_stream_ = avformat_new_stream(output_format_ctx_, nullptr);
    if (!video_stream_) {
        std::cerr << "创建视频流失败" << std::endl;
        return false;
    }

    // 复制编码器参数到流
    ret = avcodec_parameters_from_context(video_stream_->codecpar, codec_ctx_);
    if (ret < 0) {
        std::cerr << "复制编码器参数失败: " << av_error_string(ret) << std::endl;
        return false;
    }

    video_stream_->time_base = codec_ctx_->time_base;

    // 打开输出文件
    if (!(output_format_ctx_->oformat->flags & AVFMT_NOFILE)) {
        ret = avio_open(&output_format_ctx_->pb, output_file.c_str(), AVIO_FLAG_WRITE);
        if (ret < 0) {
            std::cerr << "打开输出文件失败: " << av_error_string(ret) << std::endl;
            return false;
        }
    }

    return true;
}

bool ScreenRecorder::initializeEncoder() {
    // 查找编码器
    const AVCodec* codec = avcodec_find_encoder_by_name(config_.codec.c_str());
    if (!codec) {
        std::cerr << "找不到编码器: " << config_.codec << std::endl;
        return false;
    }

    // 分配编码器上下文
    codec_ctx_ = avcodec_alloc_context3(codec);
    if (!codec_ctx_) {
        std::cerr << "分配编码器上下文失败" << std::endl;
        return false;
    }

    // 设置编码器参数
    codec_ctx_->bit_rate = config_.bitrate;
    codec_ctx_->width = config_.width;
    codec_ctx_->height = config_.height;
    codec_ctx_->time_base = {1, config_.fps};
    codec_ctx_->framerate = {config_.fps, 1};
    codec_ctx_->gop_size = config_.fps; // I帧间隔
    codec_ctx_->max_b_frames = 1;
    codec_ctx_->pix_fmt = AV_PIX_FMT_YUV420P;

    // H.264特定设置
    if (codec->id == AV_CODEC_ID_H264) {
        av_opt_set(codec_ctx_->priv_data, "preset", "medium", 0);
        av_opt_set(codec_ctx_->priv_data, "crf", "23", 0);
    }

    // 打开编码器
    int ret = avcodec_open2(codec_ctx_, codec, nullptr);
    if (ret < 0) {
        std::cerr << "打开编码器失败: " << av_error_string(ret) << std::endl;
        return false;
    }

    return true;
}

void ScreenRecorder::cleanup() {
    if (sws_ctx_) {
        sws_freeContext(sws_ctx_);
        sws_ctx_ = nullptr;
    }

    if (input_frame_) {
        av_frame_free(&input_frame_);
    }

    if (output_frame_) {
        av_frame_free(&output_frame_);
    }

    if (packet_) {
        av_packet_free(&packet_);
    }

    if (codec_ctx_) {
        avcodec_free_context(&codec_ctx_);
    }

    if (input_format_ctx_) {
        avformat_close_input(&input_format_ctx_);
    }

    if (output_format_ctx_) {
        if (output_format_ctx_->pb) {
            avio_closep(&output_format_ctx_->pb);
        }
        avformat_free_context(output_format_ctx_);
        output_format_ctx_ = nullptr;
    }
}

bool ScreenRecorder::encodeAndWriteFrame(AVFrame* frame) {
    // 发送帧到编码器
    int ret = avcodec_send_frame(codec_ctx_, frame);
    if (ret < 0) {
        std::cerr << "发送帧到编码器失败: " << av_error_string(ret) << std::endl;
        return false;
    }

    // 接收编码后的数据包
    while (ret >= 0) {
        ret = avcodec_receive_packet(codec_ctx_, packet_);
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
            break;
        } else if (ret < 0) {
            std::cerr << "接收编码数据包失败: " << av_error_string(ret) << std::endl;
            return false;
        }

        // 设置数据包的流索引
        packet_->stream_index = video_stream_->index;

        // 转换时间戳
        av_packet_rescale_ts(packet_, codec_ctx_->time_base, video_stream_->time_base);

        // 写入数据包到文件
        ret = av_interleaved_write_frame(output_format_ctx_, packet_);
        av_packet_unref(packet_);

        if (ret < 0) {
            std::cerr << "写入数据包失败: " << av_error_string(ret) << std::endl;
            return false;
        }
    }

    return true;
}
