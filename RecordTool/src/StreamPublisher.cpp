#include "StreamPublisher.h"
#include <iostream>
#include <cstring>
#include <condition_variable>

// 辅助函数：获取FFmpeg错误字符串
static std::string av_error_string(int error_code) {
    char errbuf[AV_ERROR_MAX_STRING_SIZE];
    av_strerror(error_code, errbuf, AV_ERROR_MAX_STRING_SIZE);
    return std::string(errbuf);
}

StreamPublisher::StreamPublisher(const StreamConfig& config)
    : config_(config)
    , output_format_ctx_(nullptr)
    , codec_ctx_(nullptr)
    , video_stream_(nullptr)
    , scaled_frame_(nullptr)
    , packet_(nullptr)
    , streaming_(false)
    , initialized_(false)
    , frames_sent_(0)
    , bytes_sent_(0)
    , start_time_(0)
    , frame_count_(0)
    , current_bitrate_(config.bitrate) {
}

StreamPublisher::~StreamPublisher() {
    stopStreaming();
    cleanup();
}

bool StreamPublisher::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);

    if (initialized_.load()) {
        return true;
    }

    // 注册网络协议
    avformat_network_init();

    // 初始化输出流
    if (!initializeOutputStream()) {
        std::cerr << "初始化输出流失败" << std::endl;
        return false;
    }

    // 初始化编码器
    if (!initializeEncoder()) {
        std::cerr << "初始化推流编码器失败" << std::endl;
        cleanup();
        return false;
    }

    // 分配帧和数据包
    scaled_frame_ = av_frame_alloc();
    packet_ = av_packet_alloc();

    if (!scaled_frame_ || !packet_) {
        std::cerr << "分配推流帧或数据包失败" << std::endl;
        cleanup();
        return false;
    }

    // 设置帧参数
    scaled_frame_->format = codec_ctx_->pix_fmt;
    scaled_frame_->width = config_.width;
    scaled_frame_->height = config_.height;

    // 分配帧缓冲区
    int ret = av_frame_get_buffer(scaled_frame_, 32);
    if (ret < 0) {
        std::cerr << "分配推流帧缓冲区失败: " << av_error_string(ret) << std::endl;
        cleanup();
        return false;
    }

    initialized_.store(true);
    std::cout << "推流器初始化成功，目标: " << config_.rtmp_url << std::endl;
    return true;
}

bool StreamPublisher::startStreaming() {
    if (!initialized_.load()) {
        std::cerr << "推流器未初始化" << std::endl;
        return false;
    }

    if (streaming_.load()) {
        std::cerr << "已经在推流中" << std::endl;
        return false;
    }

    // 写入流头
    int ret = avformat_write_header(output_format_ctx_, nullptr);
    if (ret < 0) {
        std::cerr << "写入推流头失败: " << av_error_string(ret) << std::endl;
        return false;
    }

    streaming_.store(true);
    start_time_ = av_gettime();
    frame_count_ = 0;
    frames_sent_.store(0);
    bytes_sent_.store(0);

    // 启动推流线程
    stream_thread_ = std::thread(&StreamPublisher::streamingThread, this);

    std::cout << "开始推流到: " << config_.rtmp_url << std::endl;
    return true;
}

void StreamPublisher::stopStreaming() {
    if (!streaming_.load()) {
        return;
    }

    streaming_.store(false);

    // 通知推流线程
    queue_cv_.notify_all();

    // 等待推流线程结束
    if (stream_thread_.joinable()) {
        stream_thread_.join();
    }

    // 写入流尾
    if (output_format_ctx_) {
        av_write_trailer(output_format_ctx_);
    }

    // 清空帧队列
    std::lock_guard<std::mutex> lock(queue_mutex_);
    while (!frame_queue_.empty()) {
        freeFrame(frame_queue_.front());
        frame_queue_.pop();
    }

    std::cout << "推流已停止" << std::endl;
}

bool StreamPublisher::pushFrame(AVFrame* frame) {
    if (!streaming_.load() || !frame) {
        return false;
    }

    // 复制帧数据
    AVFrame* frame_copy = copyFrame(frame);
    if (!frame_copy) {
        return false;
    }

    // 添加到队列
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);

        // 如果队列满了，丢弃最旧的帧
        if (frame_queue_.size() >= static_cast<size_t>(config_.buffer_size)) {
            freeFrame(frame_queue_.front());
            frame_queue_.pop();
        }

        frame_queue_.push(frame_copy);
    }

    // 通知推流线程
    queue_cv_.notify_one();

    return true;
}

void StreamPublisher::getStreamStats(int64_t& frames_sent, int64_t& bytes_sent, double& duration) const {
    frames_sent = frames_sent_.load();
    bytes_sent = bytes_sent_.load();

    if (streaming_.load() && start_time_ > 0) {
        int64_t current_time = av_gettime();
        duration = (current_time - start_time_) / 1000000.0;
    } else {
        duration = 0.0;
    }
}

void StreamPublisher::setStreamQuality(int quality) {
    if (quality < 1) quality = 1;
    if (quality > 10) quality = 10;

    // 根据质量等级调整码率
    int base_bitrate = config_.bitrate;
    int new_bitrate = base_bitrate * quality / 10;

    current_bitrate_.store(new_bitrate);

    // 如果编码器已初始化，动态调整码率
    if (codec_ctx_) {
        codec_ctx_->bit_rate = new_bitrate;
    }

    std::cout << "推流质量调整为: " << quality << "/10, 码率: " << new_bitrate << " bps" << std::endl;
}

void StreamPublisher::streamingThread() {
    std::cout << "推流线程已启动" << std::endl;

    while (streaming_.load()) {
        AVFrame* frame = nullptr;

        // 从队列获取帧
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_cv_.wait_for(lock, std::chrono::milliseconds(100), [this] {
                return !frame_queue_.empty() || !streaming_.load();
            });

            if (!frame_queue_.empty()) {
                frame = frame_queue_.front();
                frame_queue_.pop();
            }
        }

        if (frame) {
            if (encodeAndSendFrame(frame)) {
                frames_sent_.fetch_add(1);
            }
            freeFrame(frame);
        }
    }

    std::cout << "推流线程已结束" << std::endl;
}

bool StreamPublisher::initializeOutputStream() {
    // 分配输出格式上下文
    int ret = avformat_alloc_output_context2(&output_format_ctx_, nullptr, "flv", config_.rtmp_url.c_str());
    if (ret < 0) {
        std::cerr << "分配推流输出格式上下文失败: " << av_error_string(ret) << std::endl;
        return false;
    }

    // 创建视频流
    video_stream_ = avformat_new_stream(output_format_ctx_, nullptr);
    if (!video_stream_) {
        std::cerr << "创建推流视频流失败" << std::endl;
        return false;
    }

    video_stream_->id = output_format_ctx_->nb_streams - 1;

    return true;
}

bool StreamPublisher::initializeEncoder() {
    // 查找编码器
    const AVCodec* codec = avcodec_find_encoder_by_name(config_.codec.c_str());
    if (!codec) {
        std::cerr << "找不到推流编码器: " << config_.codec << std::endl;
        return false;
    }

    // 分配编码器上下文
    codec_ctx_ = avcodec_alloc_context3(codec);
    if (!codec_ctx_) {
        std::cerr << "分配推流编码器上下文失败" << std::endl;
        return false;
    }

    // 设置编码器参数（针对推流优化）
    codec_ctx_->bit_rate = current_bitrate_.load();
    codec_ctx_->width = config_.width;
    codec_ctx_->height = config_.height;
    codec_ctx_->time_base = {1, config_.fps};
    codec_ctx_->framerate = {config_.fps, 1};
    codec_ctx_->gop_size = config_.gop_size;
    codec_ctx_->max_b_frames = config_.max_b_frames;
    codec_ctx_->pix_fmt = AV_PIX_FMT_YUV420P;

    // 推流特定设置
    if (codec->id == AV_CODEC_ID_H264) {
        av_opt_set(codec_ctx_->priv_data, "preset", config_.preset.c_str(), 0);
        av_opt_set(codec_ctx_->priv_data, "tune", "zerolatency", 0);
        av_opt_set(codec_ctx_->priv_data, "profile", "baseline", 0);
        av_opt_set(codec_ctx_->priv_data, "level", "3.1", 0);
    }

    // 如果是全局头格式，设置标志
    if (output_format_ctx_->oformat->flags & AVFMT_GLOBALHEADER) {
        codec_ctx_->flags |= AV_CODEC_FLAG_GLOBAL_HEADER;
    }

    // 打开编码器
    int ret = avcodec_open2(codec_ctx_, codec, nullptr);
    if (ret < 0) {
        std::cerr << "打开推流编码器失败: " << av_error_string(ret) << std::endl;
        return false;
    }

    // 复制编码器参数到流
    ret = avcodec_parameters_from_context(video_stream_->codecpar, codec_ctx_);
    if (ret < 0) {
        std::cerr << "复制推流编码器参数失败: " << av_error_string(ret) << std::endl;
        return false;
    }

    video_stream_->time_base = codec_ctx_->time_base;

    // 打开输出URL
    ret = avio_open(&output_format_ctx_->pb, config_.rtmp_url.c_str(), AVIO_FLAG_WRITE);
    if (ret < 0) {
        std::cerr << "打开推流URL失败: " << av_error_string(ret) << std::endl;
        return false;
    }

    return true;
}

void StreamPublisher::cleanup() {
    if (scaled_frame_) {
        av_frame_free(&scaled_frame_);
    }

    if (packet_) {
        av_packet_free(&packet_);
    }

    if (codec_ctx_) {
        avcodec_free_context(&codec_ctx_);
    }

    if (output_format_ctx_) {
        if (output_format_ctx_->pb) {
            avio_closep(&output_format_ctx_->pb);
        }
        avformat_free_context(output_format_ctx_);
        output_format_ctx_ = nullptr;
    }

    avformat_network_deinit();
}

bool StreamPublisher::encodeAndSendFrame(AVFrame* frame) {
    // 设置时间戳
    frame->pts = frame_count_++;

    // 发送帧到编码器
    int ret = avcodec_send_frame(codec_ctx_, frame);
    if (ret < 0) {
        std::cerr << "发送帧到推流编码器失败: " << av_error_string(ret) << std::endl;
        return false;
    }

    // 接收编码后的数据包
    while (ret >= 0) {
        ret = avcodec_receive_packet(codec_ctx_, packet_);
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
            break;
        } else if (ret < 0) {
            std::cerr << "接收推流编码数据包失败: " << av_error_string(ret) << std::endl;
            return false;
        }

        // 设置数据包的流索引
        packet_->stream_index = video_stream_->index;

        // 转换时间戳
        av_packet_rescale_ts(packet_, codec_ctx_->time_base, video_stream_->time_base);

        // 发送数据包
        ret = av_interleaved_write_frame(output_format_ctx_, packet_);

        if (ret >= 0) {
            bytes_sent_.fetch_add(packet_->size);
        }

        av_packet_unref(packet_);

        if (ret < 0) {
            std::cerr << "发送推流数据包失败: " << av_error_string(ret) << std::endl;
            return false;
        }
    }

    return true;
}

AVFrame* StreamPublisher::copyFrame(AVFrame* src) {
    AVFrame* dst = av_frame_alloc();
    if (!dst) {
        return nullptr;
    }

    // 复制帧属性
    dst->format = src->format;
    dst->width = src->width;
    dst->height = src->height;

    // 分配缓冲区
    int ret = av_frame_get_buffer(dst, 32);
    if (ret < 0) {
        av_frame_free(&dst);
        return nullptr;
    }

    // 复制数据
    ret = av_frame_copy(dst, src);
    if (ret < 0) {
        av_frame_free(&dst);
        return nullptr;
    }

    // 复制属性
    ret = av_frame_copy_props(dst, src);
    if (ret < 0) {
        av_frame_free(&dst);
        return nullptr;
    }

    return dst;
}

void StreamPublisher::freeFrame(AVFrame* frame) {
    if (frame) {
        av_frame_free(&frame);
    }
}
