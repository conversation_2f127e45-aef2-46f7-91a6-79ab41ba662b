#include "RecordManager.h"
#include <iostream>
#include <iomanip>
#include <signal.h>
#include <thread>
#include <chrono>

// 全局变量用于信号处理
RecordManager* g_record_manager = nullptr;

/**
 * @brief 信号处理函数
 * 处理Ctrl+C等中断信号，优雅地停止录制
 */
void signalHandler(int signal) {
    std::cout << "\n收到信号 " << signal << "，正在停止录制..." << std::endl;

    if (g_record_manager) {
        g_record_manager->stop();
    }

    exit(0);
}

/**
 * @brief 打印使用说明
 */
void printUsage(const char* program_name) {
    std::cout << "用法: " << program_name << " [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -h, --help              显示此帮助信息" << std::endl;
    std::cout << "  -o, --output DIR        设置输出目录 (默认: ./recordings/)" << std::endl;
    std::cout << "  -p, --prefix PREFIX     设置文件名前缀 (默认: screen_record)" << std::endl;
    std::cout << "  -d, --duration SECONDS  设置分段时长，秒 (默认: 1800 = 30分钟)" << std::endl;
    std::cout << "  -r, --rtmp URL          设置RTMP推流地址" << std::endl;
    std::cout << "  -w, --width WIDTH       设置视频宽度 (默认: 1920)" << std::endl;
    std::cout << "  --height HEIGHT         设置视频高度 (默认: 1080)" << std::endl;
    std::cout << "  -f, --fps FPS           设置帧率 (默认: 30)" << std::endl;
    std::cout << "  --record-bitrate RATE   设置录制码率 (默认: 2000000)" << std::endl;
    std::cout << "  --stream-bitrate RATE   设置推流码率 (默认: 1500000)" << std::endl;
    std::cout << "  --no-stream             禁用推流功能" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  " << program_name << " -r rtmp://live.example.com/live/stream_key" << std::endl;
    std::cout << "  " << program_name << " -o /tmp/recordings -d 600 --no-stream" << std::endl;
}

/**
 * @brief 解析命令行参数
 */
bool parseArguments(int argc, char* argv[], RecordManager::ManagerConfig& config) {
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "-h" || arg == "--help") {
            printUsage(argv[0]);
            return false;
        } else if (arg == "-o" || arg == "--output") {
            if (i + 1 < argc) {
                config.output_dir = argv[++i];
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数" << std::endl;
                return false;
            }
        } else if (arg == "-p" || arg == "--prefix") {
            if (i + 1 < argc) {
                config.file_prefix = argv[++i];
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数" << std::endl;
                return false;
            }
        } else if (arg == "-d" || arg == "--duration") {
            if (i + 1 < argc) {
                config.segment_duration = std::stoi(argv[++i]);
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数" << std::endl;
                return false;
            }
        } else if (arg == "-r" || arg == "--rtmp") {
            if (i + 1 < argc) {
                config.rtmp_url = argv[++i];
                config.enable_streaming = true;
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数" << std::endl;
                return false;
            }
        } else if (arg == "-w" || arg == "--width") {
            if (i + 1 < argc) {
                config.width = std::stoi(argv[++i]);
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数" << std::endl;
                return false;
            }
        } else if (arg == "--height") {
            if (i + 1 < argc) {
                config.height = std::stoi(argv[++i]);
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数" << std::endl;
                return false;
            }
        } else if (arg == "-f" || arg == "--fps") {
            if (i + 1 < argc) {
                config.fps = std::stoi(argv[++i]);
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数" << std::endl;
                return false;
            }
        } else if (arg == "--record-bitrate") {
            if (i + 1 < argc) {
                config.record_bitrate = std::stoi(argv[++i]);
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数" << std::endl;
                return false;
            }
        } else if (arg == "--stream-bitrate") {
            if (i + 1 < argc) {
                config.stream_bitrate = std::stoi(argv[++i]);
            } else {
                std::cerr << "错误: " << arg << " 需要一个参数" << std::endl;
                return false;
            }
        } else if (arg == "--no-stream") {
            config.enable_streaming = false;
        } else {
            std::cerr << "错误: 未知参数 " << arg << std::endl;
            return false;
        }
    }

    return true;
}

/**
 * @brief 打印状态信息
 */
void printStatus(RecordManager* manager) {
    while (manager->getStatus() == RecordManager::RecordStatus::RECORDING) {
        // 获取录制信息
        std::string current_file;
        double file_duration, total_duration;
        manager->getCurrentFileInfo(current_file, file_duration, total_duration);

        // 获取推流信息
        bool is_streaming;
        int64_t frames_sent, bytes_sent;
        double stream_duration;
        manager->getStreamInfo(is_streaming, frames_sent, bytes_sent, stream_duration);

        // 清屏并打印状态
        std::cout << "\033[2J\033[1;1H"; // 清屏
        std::cout << "=== 屏幕录制和推流工具 ===" << std::endl;
        std::cout << "状态: 录制中" << std::endl;
        std::cout << "当前文件: " << current_file << std::endl;
        std::cout << "文件时长: " << static_cast<int>(file_duration / 60) << ":"
                  << std::setfill('0') << std::setw(2) << static_cast<int>(file_duration) % 60 << std::endl;
        std::cout << "总时长: " << static_cast<int>(total_duration / 60) << ":"
                  << std::setfill('0') << std::setw(2) << static_cast<int>(total_duration) % 60 << std::endl;

        if (is_streaming) {
            std::cout << "推流状态: 正在推流" << std::endl;
            std::cout << "已发送帧数: " << frames_sent << std::endl;
            std::cout << "已发送字节: " << bytes_sent / 1024 / 1024 << " MB" << std::endl;
            std::cout << "推流时长: " << static_cast<int>(stream_duration / 60) << ":"
                      << std::setfill('0') << std::setw(2) << static_cast<int>(stream_duration) % 60 << std::endl;
        } else {
            std::cout << "推流状态: 未启用或已断开" << std::endl;
        }

        std::cout << std::endl;
        std::cout << "按 Ctrl+C 停止录制" << std::endl;

        // 等待5秒后更新
        std::this_thread::sleep_for(std::chrono::seconds(5));
    }
}

/**
 * @brief 主函数
 */
int main(int argc, char* argv[]) {
    std::cout << "=== 屏幕录制和推流工具 v1.0 ===" << std::endl;
    std::cout << "基于 FFmpeg 的屏幕录制和实时推流工具" << std::endl;
    std::cout << std::endl;

    // 默认配置
    RecordManager::ManagerConfig config;

    // 解析命令行参数
    if (!parseArguments(argc, argv, config)) {
        return 1;
    }

    // 验证配置
    if (config.enable_streaming && config.rtmp_url.empty()) {
        std::cerr << "错误: 启用推流时必须提供RTMP地址" << std::endl;
        return 1;
    }

    // 创建录制管理器
    RecordManager manager(config);
    g_record_manager = &manager;

    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    // 设置错误回调
    manager.setErrorCallback([](const std::string& error) {
        std::cerr << "错误: " << error << std::endl;
    });

    // 初始化管理器
    if (!manager.initialize()) {
        std::cerr << "初始化失败" << std::endl;
        return 1;
    }

    // 开始录制
    if (!manager.start()) {
        std::cerr << "启动录制失败" << std::endl;
        return 1;
    }

    // 打印状态信息
    printStatus(&manager);

    // 等待录制结束
    while (manager.getStatus() != RecordManager::RecordStatus::STOPPED &&
           manager.getStatus() != RecordManager::RecordStatus::ERROR) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // 打印录制文件列表
    auto recorded_files = manager.getRecordedFiles();
    if (!recorded_files.empty()) {
        std::cout << "\n已录制的文件:" << std::endl;
        for (const auto& file : recorded_files) {
            std::cout << "  " << file << std::endl;
        }
    }

    std::cout << "程序结束" << std::endl;
    return 0;
}
