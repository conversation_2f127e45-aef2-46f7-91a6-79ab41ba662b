#ifndef SCREEN_RECORDER_H
#define SCREEN_RECORDER_H

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/avutil.h>
#include <libavdevice/avdevice.h>
#include <libswscale/swscale.h>
#include <libavutil/imgutils.h>
#include <libavutil/time.h>
#include <libavutil/opt.h>
}

#include <X11/Xlib.h>
#include <X11/Xutil.h>
#include <X11/extensions/XShm.h>

#include <string>
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>

/**
 * @brief 屏幕录制器类
 *
 * 负责捕获屏幕内容并编码为视频流
 * 支持X11屏幕捕获和H.264编码
 */
class ScreenRecorder {
public:
    /**
     * @brief 录制配置结构体
     */
    struct RecordConfig {
        int width = 1920;           // 录制宽度
        int height = 1080;          // 录制高度
        int fps = 30;               // 帧率
        int bitrate = 2000000;      // 比特率 (2Mbps)
        std::string codec = "libx264";  // 编码器
        std::string pixel_format = "yuv420p";  // 像素格式
        std::string display = ":0"; // X11显示器
    };

    /**
     * @brief 构造函数
     * @param config 录制配置
     */
    explicit ScreenRecorder(const RecordConfig& config);

    /**
     * @brief 析构函数
     */
    ~ScreenRecorder();

    /**
     * @brief 初始化录制器
     * @return 成功返回true，失败返回false
     */
    bool initialize();

    /**
     * @brief 开始录制到文件
     * @param output_file 输出文件路径
     * @return 成功返回true，失败返回false
     */
    bool startRecording(const std::string& output_file);

    /**
     * @brief 停止录制
     */
    void stopRecording();

    /**
     * @brief 获取当前帧数据
     * @param frame 输出帧数据
     * @return 成功返回true，失败返回false
     */
    bool captureFrame(AVFrame*& frame);

    /**
     * @brief 检查是否正在录制
     * @return 正在录制返回true，否则返回false
     */
    bool isRecording() const { return recording_.load(); }

    /**
     * @brief 获取录制时长（秒）
     * @return 录制时长
     */
    double getRecordingDuration() const;

private:
    RecordConfig config_;                   // 录制配置

    // FFmpeg相关
    AVFormatContext* input_format_ctx_;     // 输入格式上下文
    AVFormatContext* output_format_ctx_;    // 输出格式上下文
    AVCodecContext* codec_ctx_;             // 编码器上下文
    AVStream* video_stream_;                // 视频流
    SwsContext* sws_ctx_;                   // 图像转换上下文

    // 帧相关
    AVFrame* input_frame_;                  // 输入帧
    AVFrame* output_frame_;                 // 输出帧
    AVPacket* packet_;                      // 数据包

    // 状态控制
    std::atomic<bool> recording_;           // 录制状态
    std::atomic<bool> initialized_;         // 初始化状态
    std::thread record_thread_;             // 录制线程
    mutable std::mutex mutex_;              // 互斥锁

    // 时间相关
    int64_t start_time_;                    // 开始时间
    int64_t frame_count_;                   // 帧计数

    // X11相关
    Display* display_;                      // X11显示器
    Window root_window_;                    // 根窗口
    XImage* x_image_;                       // X11图像
    int screen_width_;                      // 屏幕宽度
    int screen_height_;                     // 屏幕高度

    /**
     * @brief 录制线程函数
     */
    void recordingThread();

    /**
     * @brief 初始化输入设备
     * @return 成功返回true，失败返回false
     */
    bool initializeInput();

    /**
     * @brief 初始化输出文件
     * @param output_file 输出文件路径
     * @return 成功返回true，失败返回false
     */
    bool initializeOutput(const std::string& output_file);

    /**
     * @brief 初始化编码器
     * @return 成功返回true，失败返回false
     */
    bool initializeEncoder();

    /**
     * @brief 清理资源
     */
    void cleanup();

    /**
     * @brief 编码并写入帧
     * @param frame 要编码的帧
     * @return 成功返回true，失败返回false
     */
    bool encodeAndWriteFrame(AVFrame* frame);

    /**
     * @brief 生成测试帧数据
     */
    void generateTestFrame();

    /**
     * @brief 初始化X11屏幕捕获
     * @return 成功返回true，失败返回false
     */
    bool initializeX11();

    /**
     * @brief 捕获屏幕图像
     * @return 成功返回true，失败返回false
     */
    bool captureScreen();

    /**
     * @brief 清理X11资源
     */
    void cleanupX11();

    /**
     * @brief 转换RGB到YUV格式
     */
    void convertRGBToYUV();
};

#endif // SCREEN_RECORDER_H
