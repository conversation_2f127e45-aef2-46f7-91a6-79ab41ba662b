#ifndef STREAM_PUBLISHER_H
#define STREAM_PUBLISHER_H

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/avutil.h>
#include <libavutil/time.h>
}

#include <string>
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <queue>

/**
 * @brief 流媒体推送器类
 * 
 * 负责将视频帧推送到RTMP服务器进行实时直播
 * 支持多种流媒体协议和自适应码率
 */
class StreamPublisher {
public:
    /**
     * @brief 推流配置结构体
     */
    struct StreamConfig {
        std::string rtmp_url;           // RTMP推流地址
        int width = 1920;               // 视频宽度
        int height = 1080;              // 视频高度
        int fps = 30;                   // 帧率
        int bitrate = 1500000;          // 比特率 (1.5Mbps，适合推流)
        std::string codec = "libx264";  // 编码器
        std::string pixel_format = "yuv420p";  // 像素格式
        std::string preset = "ultrafast";      // 编码预设
        int gop_size = 60;              // GOP大小
        int max_b_frames = 0;           // B帧数量（推流建议为0）
        int buffer_size = 10;           // 缓冲区大小
    };

    /**
     * @brief 构造函数
     * @param config 推流配置
     */
    explicit StreamPublisher(const StreamConfig& config);
    
    /**
     * @brief 析构函数
     */
    ~StreamPublisher();

    /**
     * @brief 初始化推流器
     * @return 成功返回true，失败返回false
     */
    bool initialize();

    /**
     * @brief 开始推流
     * @return 成功返回true，失败返回false
     */
    bool startStreaming();

    /**
     * @brief 停止推流
     */
    void stopStreaming();

    /**
     * @brief 推送视频帧
     * @param frame 要推送的帧数据
     * @return 成功返回true，失败返回false
     */
    bool pushFrame(AVFrame* frame);

    /**
     * @brief 检查是否正在推流
     * @return 正在推流返回true，否则返回false
     */
    bool isStreaming() const { return streaming_.load(); }

    /**
     * @brief 获取推流统计信息
     * @param frames_sent 已发送帧数
     * @param bytes_sent 已发送字节数
     * @param duration 推流时长（秒）
     */
    void getStreamStats(int64_t& frames_sent, int64_t& bytes_sent, double& duration) const;

    /**
     * @brief 设置推流质量（动态调整码率）
     * @param quality 质量等级 (1-10, 10为最高质量)
     */
    void setStreamQuality(int quality);

private:
    StreamConfig config_;                   // 推流配置
    
    // FFmpeg相关
    AVFormatContext* output_format_ctx_;    // 输出格式上下文
    AVCodecContext* codec_ctx_;             // 编码器上下文
    AVStream* video_stream_;                // 视频流
    
    // 帧相关
    AVFrame* scaled_frame_;                 // 缩放后的帧
    AVPacket* packet_;                      // 数据包
    
    // 状态控制
    std::atomic<bool> streaming_;           // 推流状态
    std::atomic<bool> initialized_;         // 初始化状态
    std::thread stream_thread_;             // 推流线程
    mutable std::mutex mutex_;              // 互斥锁
    
    // 帧缓冲队列
    std::queue<AVFrame*> frame_queue_;      // 帧队列
    std::mutex queue_mutex_;                // 队列互斥锁
    std::condition_variable queue_cv_;      // 队列条件变量
    
    // 统计信息
    std::atomic<int64_t> frames_sent_;      // 已发送帧数
    std::atomic<int64_t> bytes_sent_;       // 已发送字节数
    int64_t start_time_;                    // 开始时间
    int64_t frame_count_;                   // 帧计数
    
    // 质量控制
    std::atomic<int> current_bitrate_;      // 当前码率
    
    /**
     * @brief 推流线程函数
     */
    void streamingThread();
    
    /**
     * @brief 初始化输出流
     * @return 成功返回true，失败返回false
     */
    bool initializeOutputStream();
    
    /**
     * @brief 初始化编码器
     * @return 成功返回true，失败返回false
     */
    bool initializeEncoder();
    
    /**
     * @brief 清理资源
     */
    void cleanup();
    
    /**
     * @brief 编码并发送帧
     * @param frame 要编码的帧
     * @return 成功返回true，失败返回false
     */
    bool encodeAndSendFrame(AVFrame* frame);
    
    /**
     * @brief 复制帧数据
     * @param src 源帧
     * @return 复制的帧，失败返回nullptr
     */
    AVFrame* copyFrame(AVFrame* src);
    
    /**
     * @brief 释放帧
     * @param frame 要释放的帧
     */
    void freeFrame(AVFrame* frame);
};

#endif // STREAM_PUBLISHER_H
