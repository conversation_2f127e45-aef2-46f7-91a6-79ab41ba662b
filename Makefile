# 简单的Makefile用于测试编译
# 屏幕录制和推流工具

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -Iinclude

# FFmpeg库（如果系统有的话）
FFMPEG_LIBS = -lavcodec -lavformat -lavutil -lavdevice -lavfilter -lswscale -lswresample

# X11库
X11_LIBS = -lX11 -lXext -lXfixes

# 其他库
OTHER_LIBS = -lpthread

# 所有库
LIBS = $(FFMPEG_LIBS) $(X11_LIBS) $(OTHER_LIBS)

# 源文件
SOURCES = src/main.cpp src/ScreenRecorder.cpp src/StreamPublisher.cpp src/RecordManager.cpp

# 目标文件
OBJECTS = $(SOURCES:.cpp=.o)

# 可执行文件
TARGET = RecordTool

# 默认目标
all: $(TARGET)

# 链接可执行文件
$(TARGET): $(OBJECTS)
	@echo "链接 $@..."
	$(CXX) $(OBJECTS) -o $@ $(LIBS)
	@echo "编译完成: $@"

# 编译源文件
%.o: %.cpp
	@echo "编译 $<..."
	$(CXX) $(CXXFLAGS) -c $< -o $@

# 清理
clean:
	@echo "清理编译文件..."
	rm -f $(OBJECTS) $(TARGET)
	rm -rf build/

# 测试编译（不链接FFmpeg，仅检查语法）
test-compile:
	@echo "测试编译（仅检查语法）..."
	$(CXX) $(CXXFLAGS) -DTEST_COMPILE_ONLY -c src/main.cpp -o /tmp/test_main.o
	$(CXX) $(CXXFLAGS) -DTEST_COMPILE_ONLY -c src/ScreenRecorder.cpp -o /tmp/test_screen.o
	$(CXX) $(CXXFLAGS) -DTEST_COMPILE_ONLY -c src/StreamPublisher.cpp -o /tmp/test_stream.o
	$(CXX) $(CXXFLAGS) -DTEST_COMPILE_ONLY -c src/RecordManager.cpp -o /tmp/test_manager.o
	@echo "语法检查通过!"
	rm -f /tmp/test_*.o

# 检查依赖
check-deps:
	@echo "检查编译依赖..."
	@echo -n "检查g++: "
	@which g++ > /dev/null && echo "✓ 找到" || echo "✗ 未找到"
	@echo -n "检查FFmpeg库: "
	@pkg-config --exists libavcodec 2>/dev/null && echo "✓ 找到" || echo "✗ 未找到"
	@echo -n "检查X11库: "
	@pkg-config --exists x11 2>/dev/null && echo "✓ 找到" || echo "✗ 未找到"

# 安装依赖提示
install-deps-help:
	@echo "=== 依赖安装指南 ==="
	@echo ""
	@echo "Ubuntu/Debian:"
	@echo "sudo apt-get update"
	@echo "sudo apt-get install build-essential"
	@echo "sudo apt-get install libavcodec-dev libavformat-dev libavutil-dev \\"
	@echo "                     libavdevice-dev libavfilter-dev libswscale-dev \\"
	@echo "                     libswresample-dev libx11-dev libxext-dev libxfixes-dev"
	@echo ""
	@echo "CentOS/RHEL:"
	@echo "sudo yum groupinstall 'Development Tools'"
	@echo "sudo yum install epel-release"
	@echo "sudo yum install ffmpeg-devel libX11-devel libXext-devel libXfixes-devel"
	@echo ""
	@echo "Fedora:"
	@echo "sudo dnf groupinstall 'Development Tools'"
	@echo "sudo dnf install ffmpeg-devel libX11-devel libXext-devel libXfixes-devel"

# 创建录制目录
setup:
	@echo "创建录制目录..."
	mkdir -p recordings
	@echo "设置完成"

# 显示帮助
help:
	@echo "可用的make目标:"
	@echo "  all              - 编译完整程序"
	@echo "  test-compile     - 仅测试编译（检查语法）"
	@echo "  check-deps       - 检查依赖库"
	@echo "  install-deps-help - 显示依赖安装指南"
	@echo "  setup            - 创建必要的目录"
	@echo "  clean            - 清理编译文件"
	@echo "  help             - 显示此帮助"

.PHONY: all clean test-compile check-deps install-deps-help setup help
